#!/usr/bin/env node

/**
 * Pangea Website 關鍵功能效能基準測試
 * 針對表單提交、PayUni 支付、Google Sheets 操作進行深度測試
 */

import { cpuMonitor } from '../lib/cpu-time-monitor.js';
import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';

class KeyFunctionBenchmark {
  constructor() {
    this.benchmarkResults = [];
    this.iterations = 10; // 每個測試執行次數
  }

  /**
   * 執行所有關鍵功能基準測試
   */
  async runBenchmarks() {
    console.log('🎯 開始關鍵功能效能基準測試...\n');
    
    cpuMonitor.clear();
    
    const benchmarks = [
      { name: 'ContactFormSubmission', fn: () => this.benchmarkContactForm() },
      { name: 'EventRegistration', fn: () => this.benchmarkEventRegistration() },
      { name: 'PayUniOrderCreation', fn: () => this.benchmarkPayUniOrder() },
      { name: 'PayUniPaymentQuery', fn: () => this.benchmarkPayUniQuery() },
      { name: 'GoogleSheetsWrite', fn: () => this.benchmarkGoogleSheetsWrite() },
      { name: 'GoogleSheetsRead', fn: () => this.benchmarkGoogleSheetsRead() },
      { name: 'FormValidation', fn: () => this.benchmarkFormValidation() },
      { name: 'DataProcessing', fn: () => this.benchmarkDataProcessing() }
    ];
    
    for (const benchmark of benchmarks) {
      await this.runSingleBenchmark(benchmark);
    }
    
    const report = await this.generateBenchmarkReport();
    return report;
  }

  /**
   * 執行單個基準測試
   */
  async runSingleBenchmark(benchmark) {
    console.log(`📊 基準測試: ${benchmark.name}`);
    
    const results = [];
    
    for (let i = 0; i < this.iterations; i++) {
      try {
        const result = await cpuMonitor.measureCPUTime(
          `${benchmark.name}_${i + 1}`,
          benchmark.fn
        );
        results.push(result);
        process.stdout.write('.');
      } catch (error) {
        console.error(`\n❌ 測試失敗 ${benchmark.name} #${i + 1}:`, error.message);
      }
    }
    
    console.log(` 完成 (${results.length}/${this.iterations})\n`);
    
    this.benchmarkResults.push({
      name: benchmark.name,
      iterations: this.iterations,
      successful: results.length,
      results
    });
  }

  /**
   * 基準測試：聯絡表單提交
   */
  async benchmarkContactForm() {
    const formData = {
      name: '測試用戶',
      email: '<EMAIL>',
      phone: '0912345678',
      message: '這是一個測試訊息，包含一些中文字符和特殊符號！@#$%^&*()'
    };
    
    // 模擬表單驗證
    const validations = [
      this.validateEmail(formData.email),
      this.validatePhone(formData.phone),
      this.validateName(formData.name),
      this.validateMessage(formData.message)
    ];
    
    // 模擬資料清理
    const cleanedData = {
      name: formData.name.trim(),
      email: formData.email.toLowerCase().trim(),
      phone: formData.phone.replace(/\D/g, ''),
      message: formData.message.trim()
    };
    
    // 模擬資料序列化
    const serialized = JSON.stringify(cleanedData);
    const parsed = JSON.parse(serialized);
    
    return {
      valid: validations.every(v => v),
      data: parsed,
      size: serialized.length
    };
  }

  /**
   * 基準測試：活動報名
   */
  async benchmarkEventRegistration() {
    const registrationData = {
      name: '測試參與者',
      email: '<EMAIL>',
      phone: '0987654321',
      session: '2025-02-01-morning',
      participants: 2,
      specialRequests: '素食、輪椅通道'
    };
    
    // 模擬複雜的業務邏輯
    const sessionValidation = this.validateSession(registrationData.session);
    const capacityCheck = await this.checkSessionCapacity(registrationData.session);
    const priceCalculation = this.calculatePrice(registrationData.participants);
    
    // 模擬資料庫操作
    const orderNumber = this.generateOrderNumber();
    
    return {
      orderNumber,
      sessionValid: sessionValidation,
      hasCapacity: capacityCheck,
      totalPrice: priceCalculation,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 基準測試：PayUni 訂單建立
   */
  async benchmarkPayUniOrder() {
    const orderData = {
      amount: 1500,
      orderNumber: `ORD_${Date.now()}`,
      customerEmail: '<EMAIL>',
      customerName: '測試客戶',
      items: [
        { name: '錶匠體驗課程', price: 1500, quantity: 1 }
      ]
    };
    
    // 模擬 PayUni 加密處理
    const crypto = await import('crypto');
    
    // 模擬輕量級加密（避免計算密集的 scrypt）
    const data = JSON.stringify(orderData);

    // 使用簡單的 hash 模擬加密過程
    const hash = crypto.createHash('sha256');
    hash.update(data);
    const encrypted = hash.digest('hex');
    
    // 模擬 HMAC 簽名
    const hmac = crypto.createHmac('sha256', 'test-secret');
    hmac.update(encrypted);
    const signature = hmac.digest('hex');
    
    return {
      orderId: orderData.orderNumber,
      encryptedData: encrypted,
      signature,
      timestamp: Date.now()
    };
  }

  /**
   * 基準測試：PayUni 支付查詢
   */
  async benchmarkPayUniQuery() {
    const queryData = {
      orderNumber: `ORD_${Date.now()}`,
      merchantId: 'TEST_MERCHANT'
    };
    
    // 模擬 API 請求準備
    const crypto = await import('crypto');
    const queryString = Object.entries(queryData)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    
    // 模擬簽名計算
    const hash = crypto.createHash('sha256');
    hash.update(queryString + 'secret-key');
    const checksum = hash.digest('hex');
    
    // 模擬回應解析
    const mockResponse = {
      status: 'SUCCESS',
      orderNumber: queryData.orderNumber,
      paymentStatus: 'PAID',
      amount: 1500,
      paymentTime: new Date().toISOString()
    };
    
    return {
      query: queryData,
      checksum,
      response: mockResponse
    };
  }

  /**
   * 基準測試：Google Sheets 寫入
   */
  async benchmarkGoogleSheetsWrite() {
    const sheetsData = {
      range: 'A1:F1',
      values: [[
        new Date().toISOString(),
        '測試用戶',
        '<EMAIL>',
        '0912345678',
        '已確認',
        '1500'
      ]]
    };
    
    // 模擬資料驗證和格式化
    const formattedData = sheetsData.values.map(row => 
      row.map(cell => {
        if (typeof cell === 'string') {
          return cell.trim().replace(/"/g, '""'); // CSV 轉義
        }
        return cell;
      })
    );
    
    // 模擬批次處理
    const batchData = {
      valueInputOption: 'RAW',
      data: [{
        range: sheetsData.range,
        values: formattedData
      }]
    };
    
    // 模擬 JSON 序列化
    const serialized = JSON.stringify(batchData);
    
    return {
      rowsProcessed: formattedData.length,
      dataSize: serialized.length,
      range: sheetsData.range
    };
  }

  /**
   * 基準測試：Google Sheets 讀取
   */
  async benchmarkGoogleSheetsRead() {
    // 模擬大量資料讀取
    const mockSheetData = Array.from({ length: 200 }, (_, i) => [
      `2025-01-${String(i % 31 + 1).padStart(2, '0')}`,
      `用戶${i}`,
      `user${i}@example.com`,
      `091234${String(i).padStart(4, '0')}`,
      i % 3 === 0 ? '已確認' : '待確認',
      String(1500 + (i % 5) * 500)
    ]);
    
    // 模擬資料過濾
    const confirmedUsers = mockSheetData.filter(row => row[4] === '已確認');
    
    // 模擬資料轉換
    const processedData = confirmedUsers.map(row => ({
      date: row[0],
      name: row[1],
      email: row[2],
      phone: row[3],
      status: row[4],
      amount: parseInt(row[5])
    }));
    
    // 模擬統計計算
    const totalAmount = processedData.reduce((sum, item) => sum + item.amount, 0);
    const averageAmount = totalAmount / processedData.length;
    
    return {
      totalRows: mockSheetData.length,
      confirmedRows: confirmedUsers.length,
      processedData: processedData.length,
      totalAmount,
      averageAmount
    };
  }

  /**
   * 基準測試：表單驗證
   */
  async benchmarkFormValidation() {
    const testForms = [
      {
        name: '張三',
        email: '<EMAIL>',
        phone: '0912345678',
        message: '我想了解更多關於錶匠課程的資訊'
      },
      {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+886987654321',
        message: 'I am interested in the watch making course'
      },
      {
        name: '',
        email: 'invalid-email',
        phone: '123',
        message: ''
      }
    ];
    
    const validationResults = [];
    
    for (const form of testForms) {
      const result = {
        form,
        validations: {
          name: this.validateName(form.name),
          email: this.validateEmail(form.email),
          phone: this.validatePhone(form.phone),
          message: this.validateMessage(form.message)
        }
      };
      
      result.isValid = Object.values(result.validations).every(v => v);
      validationResults.push(result);
    }
    
    return {
      totalForms: testForms.length,
      validForms: validationResults.filter(r => r.isValid).length,
      results: validationResults
    };
  }

  /**
   * 基準測試：資料處理
   */
  async benchmarkDataProcessing() {
    // 模擬複雜的資料處理操作
    const rawData = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      timestamp: Date.now() - (i * 60000),
      value: Math.random() * 100,
      category: ['A', 'B', 'C'][i % 3],
      metadata: {
        source: 'test',
        processed: false,
        tags: [`tag${i % 10}`, `category${i % 3}`]
      }
    }));
    
    // 資料過濾
    const filtered = rawData.filter(item => item.value > 50);
    
    // 資料分組
    const grouped = filtered.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    }, {});
    
    // 統計計算
    const statistics = Object.entries(grouped).map(([category, items]) => ({
      category,
      count: items.length,
      average: items.reduce((sum, item) => sum + item.value, 0) / items.length,
      max: Math.max(...items.map(item => item.value)),
      min: Math.min(...items.map(item => item.value))
    }));
    
    return {
      originalCount: rawData.length,
      filteredCount: filtered.length,
      categories: Object.keys(grouped).length,
      statistics
    };
  }

  // 輔助驗證函數
  validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  validatePhone(phone) {
    const cleaned = phone.replace(/\D/g, '');
    return /^(09\d{8}|886\d{9})$/.test(cleaned);
  }

  validateName(name) {
    return name && name.trim().length > 0;
  }

  validateMessage(message) {
    return message && message.trim().length > 0 && message.length <= 1000;
  }

  validateSession(session) {
    const sessionPattern = /^\d{4}-\d{2}-\d{2}-(morning|afternoon|evening)$/;
    return sessionPattern.test(session);
  }

  async checkSessionCapacity(session) {
    // 模擬容量檢查
    await new Promise(resolve => setTimeout(resolve, 1));
    return Math.random() > 0.1; // 90% 機率有容量
  }

  calculatePrice(participants) {
    const basePrice = 1500;
    const discount = participants > 1 ? 0.1 : 0;
    return Math.round(basePrice * participants * (1 - discount));
  }

  generateOrderNumber() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `ORD_${timestamp}_${random}`.toUpperCase();
  }

  /**
   * 生成基準測試報告（使用固定檔名）
   */
  async generateBenchmarkReport() {
    const cpuReport = cpuMonitor.getReport();

    // 計算每個基準測試的統計資料
    const benchmarkStats = this.benchmarkResults.map(benchmark => {
      const cpuTimes = cpuReport.details
        .filter(detail => detail.functionName.startsWith(benchmark.name))
        .map(detail => detail.cpuTime);

      if (cpuTimes.length === 0) {
        return {
          name: benchmark.name,
          error: 'No CPU measurements found'
        };
      }

      const avg = cpuTimes.reduce((sum, time) => sum + time, 0) / cpuTimes.length;
      const min = Math.min(...cpuTimes);
      const max = Math.max(...cpuTimes);
      const median = cpuTimes.sort((a, b) => a - b)[Math.floor(cpuTimes.length / 2)];

      return {
        name: benchmark.name,
        iterations: benchmark.iterations,
        successful: benchmark.successful,
        cpuTime: {
          average: avg,
          min,
          max,
          median,
          all: cpuTimes
        },
        status: max > 10 ? 'EXCEEDED' : max > 8 ? 'WARNING' : 'OK'
      };
    });

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalBenchmarks: this.benchmarkResults.length,
        passed: benchmarkStats.filter(s => s.status === 'OK').length,
        warnings: benchmarkStats.filter(s => s.status === 'WARNING').length,
        exceeded: benchmarkStats.filter(s => s.status === 'EXCEEDED').length
      },
      benchmarks: benchmarkStats,
      cpuReport,
      recommendations: this.generateOptimizationRecommendations(benchmarkStats)
    };

    // 使用固定檔名儲存報告
    const reportPath = 'benchmark-report.json';

    // 備份現有報告
    await this.backupExistingReport(reportPath);

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    console.log(`📊 基準測試報告已儲存至: ${reportPath}`);

    this.displayBenchmarkSummary(report);

    return report;
  }

  /**
   * 備份現有報告並管理歷史檔案
   */
  async backupExistingReport(reportPath) {
    try {
      // 檢查檔案是否存在
      await fs.access(reportPath);

      // 建立備份檔名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = reportPath.replace('.json', `-backup-${timestamp}.json`);

      // 複製現有報告為備份
      await fs.copyFile(reportPath, backupPath);

      // 清理舊的備份檔案（保留最近 5 份）
      await this.cleanupOldBackups('.', 'benchmark-report', 5);

    } catch (error) {
      // 檔案不存在，無需備份
    }
  }

  /**
   * 清理舊的備份檔案
   */
  async cleanupOldBackups(dir, baseName, maxBackups = 5) {
    try {
      const files = await fs.readdir(dir);
      const backupFiles = files
        .filter(file => file.startsWith(`${baseName}-backup-`) && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(dir, file),
          time: fs.stat(path.join(dir, file)).then(stats => stats.mtime)
        }));

      // 等待所有檔案時間資訊
      for (const file of backupFiles) {
        file.time = await file.time;
      }

      // 按時間排序（最新的在前）
      backupFiles.sort((a, b) => b.time - a.time);

      // 刪除超過限制的舊檔案
      const filesToDelete = backupFiles.slice(maxBackups);
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
        console.log(`🗑️ 已清理舊備份: ${file.name}`);
      }
    } catch (error) {
      console.warn('清理備份檔案時發生錯誤:', error.message);
    }
  }

  /**
   * 生成優化建議
   */
  generateOptimizationRecommendations(benchmarkStats) {
    const recommendations = [];
    
    benchmarkStats.forEach(benchmark => {
      if (benchmark.status === 'EXCEEDED') {
        recommendations.push({
          function: benchmark.name,
          issue: `CPU 時間超過限制 (${benchmark.cpuTime.max.toFixed(2)}ms > 10ms)`,
          suggestions: this.getOptimizationSuggestions(benchmark.name)
        });
      } else if (benchmark.status === 'WARNING') {
        recommendations.push({
          function: benchmark.name,
          issue: `CPU 時間接近限制 (${benchmark.cpuTime.max.toFixed(2)}ms)`,
          suggestions: ['監控效能趨勢', '考慮預先優化']
        });
      }
    });
    
    return recommendations;
  }

  /**
   * 獲取特定函數的優化建議
   */
  getOptimizationSuggestions(functionName) {
    const suggestions = {
      'ContactFormSubmission': [
        '簡化表單驗證邏輯',
        '使用更高效的正則表達式',
        '減少字串操作次數'
      ],
      'EventRegistration': [
        '快取容量檢查結果',
        '優化價格計算邏輯',
        '減少同步資料庫查詢'
      ],
      'PayUniOrderCreation': [
        '使用更快的加密演算法',
        '預先計算常用的加密參數',
        '減少記憶體分配'
      ],
      'GoogleSheetsWrite': [
        '批次處理多筆資料',
        '減少資料序列化次數',
        '使用更簡潔的資料格式'
      ],
      'GoogleSheetsRead': [
        '限制讀取的資料量',
        '使用增量讀取',
        '優化資料過濾邏輯'
      ]
    };
    
    return suggestions[functionName] || [
      '減少計算複雜度',
      '優化演算法效率',
      '考慮非同步處理'
    ];
  }

  /**
   * 顯示基準測試摘要
   */
  displayBenchmarkSummary(report) {
    console.log('\n🎯 關鍵功能效能基準測試報告');
    console.log('='.repeat(60));
    console.log(`測試時間: ${report.timestamp}`);
    console.log(`總基準測試: ${report.summary.totalBenchmarks}`);
    console.log(`通過: ${report.summary.passed} ✅`);
    console.log(`警告: ${report.summary.warnings} ⚠️`);
    console.log(`超限: ${report.summary.exceeded} ❌`);
    
    console.log('\n📊 各功能效能表現:');
    report.benchmarks.forEach(benchmark => {
      if (benchmark.error) {
        console.log(`❌ ${benchmark.name}: ${benchmark.error}`);
        return;
      }
      
      const statusIcon = {
        'OK': '✅',
        'WARNING': '⚠️',
        'EXCEEDED': '❌'
      }[benchmark.status];
      
      console.log(
        `${statusIcon} ${benchmark.name}: ` +
        `平均 ${benchmark.cpuTime.average.toFixed(2)}ms, ` +
        `最大 ${benchmark.cpuTime.max.toFixed(2)}ms ` +
        `(${benchmark.successful}/${benchmark.iterations} 成功)`
      );
    });
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 優化建議:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.function}: ${rec.issue}`);
        rec.suggestions.forEach(suggestion => {
          console.log(`   - ${suggestion}`);
        });
      });
    }
    
    console.log('\n='.repeat(60));
  }
}

// 如果直接執行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
  const benchmark = new KeyFunctionBenchmark();
  
  benchmark.runBenchmarks()
    .then(report => {
      const hasExceeded = report.summary.exceeded > 0;
      process.exit(hasExceeded ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ 基準測試執行失敗:', error);
      process.exit(1);
    });
}

export { KeyFunctionBenchmark };
