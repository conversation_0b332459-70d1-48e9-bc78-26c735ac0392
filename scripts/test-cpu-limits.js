#!/usr/bin/env node

/**
 * 自動化 CPU 限制測試腳本
 * 批量測試所有 API 端點並生成詳細報告
 */

import { cpuMonitor } from '../lib/cpu-time-monitor.js';
import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CPULimitTester {
  constructor() {
    this.testResults = [];
    this.apiEndpoints = [];
    this.testData = this.getTestData();
  }

  /**
   * 獲取測試資料
   */
  getTestData() {
    return {
      // 表單提交測試資料
      contactForm: {
        name: '測試用戶',
        email: '<EMAIL>',
        phone: '0912345678',
        message: '這是一個測試訊息'
      },
      
      // 活動報名測試資料
      eventRegistration: {
        name: '測試參與者',
        email: '<EMAIL>',
        phone: '0987654321',
        session: '2025-02-01-morning',
        participants: 2
      },
      
      // PayUni 支付測試資料
      paymentData: {
        amount: 1000,
        orderNumber: `TEST_${Date.now()}`,
        customerEmail: '<EMAIL>',
        customerName: '測試客戶'
      },
      
      // Google Sheets 測試資料
      sheetsData: {
        range: 'A1:E10',
        values: [['測試', '資料', '寫入', '操作', '時間']]
      }
    };
  }

  /**
   * 發現並載入所有 API 端點
   */
  async discoverAPIEndpoints() {
    const apiDir = path.join(__dirname, '../src/app/api');
    const endpoints = [];
    
    try {
      const apiRoutes = await this.scanDirectory(apiDir);
      
      for (const route of apiRoutes) {
        try {
          // 跳過 TypeScript 檔案，因為需要編譯
          if (route.path.endsWith('.ts')) {
            console.log(`⏭️ 跳過 TypeScript 路由: ${route.name} (需要編譯)`);
            continue;
          }

          const module = await import(route.path);
          if (module.GET || module.POST || module.PUT || module.DELETE) {
            endpoints.push({
              name: route.name,
              path: route.path,
              methods: Object.keys(module).filter(key =>
                ['GET', 'POST', 'PUT', 'DELETE'].includes(key)
              ),
              module
            });
          }
        } catch (error) {
          console.warn(`⚠️ 無法載入 API 路由: ${route.path}`, error.message);
        }
      }
    } catch (error) {
      console.error('❌ 掃描 API 目錄失敗:', error.message);
    }
    
    this.apiEndpoints = endpoints;
    return endpoints;
  }

  /**
   * 掃描目錄尋找 API 檔案
   */
  async scanDirectory(dir) {
    const routes = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          const subRoutes = await this.scanDirectory(fullPath);
          routes.push(...subRoutes);
        } else if (entry.name === 'route.js' || entry.name === 'route.ts') {
          const routeName = path.relative(
            path.join(__dirname, '../src/app/api'),
            dir
          ).replace(/\\/g, '/');
          
          routes.push({
            name: routeName || 'root',
            path: fullPath
          });
        }
      }
    } catch (error) {
      // 目錄不存在或無法讀取
    }
    
    return routes;
  }

  /**
   * 測試單個 API 端點
   */
  async testAPIEndpoint(endpoint, method = 'POST') {
    const testName = `${endpoint.name}:${method}`;
    console.log(`🧪 測試 ${testName}...`);
    
    try {
      const handler = endpoint.module[method];
      if (!handler) {
        console.log(`⏭️ 跳過 ${testName} (方法不存在)`);
        return null;
      }
      
      // 建立模擬請求
      const mockRequest = this.createMockRequest(endpoint.name, method);
      const mockContext = { params: {} };
      
      // 使用 CPU 監控器測量執行時間
      const result = await cpuMonitor.measureCPUTime(
        testName,
        handler,
        mockRequest,
        mockContext
      );
      
      return {
        endpoint: testName,
        success: true,
        result
      };
      
    } catch (error) {
      console.error(`❌ 測試 ${testName} 失敗:`, error.message);
      return {
        endpoint: testName,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 建立模擬請求物件
   */
  createMockRequest(endpointName, method) {
    const testData = this.getTestDataForEndpoint(endpointName);
    
    return {
      method,
      url: `http://localhost:3000/api/${endpointName}`,
      headers: new Map([
        ['content-type', 'application/json'],
        ['user-agent', 'CPU-Limit-Tester/1.0']
      ]),
      json: async () => testData,
      text: async () => JSON.stringify(testData),
      formData: async () => {
        const formData = new FormData();
        Object.entries(testData).forEach(([key, value]) => {
          formData.append(key, value);
        });
        return formData;
      }
    };
  }

  /**
   * 根據端點名稱獲取對應的測試資料
   */
  getTestDataForEndpoint(endpointName) {
    const dataMap = {
      'contact': this.testData.contactForm,
      'register': this.testData.eventRegistration,
      'payment': this.testData.paymentData,
      'sheets': this.testData.sheetsData
    };
    
    // 尋找匹配的測試資料
    for (const [key, data] of Object.entries(dataMap)) {
      if (endpointName.includes(key)) {
        return data;
      }
    }
    
    // 預設測試資料
    return { test: true, timestamp: Date.now() };
  }

  /**
   * 執行所有測試
   */
  async runAllTests() {
    console.log('🚀 開始 CPU 限制測試...\n');
    
    // 清除之前的結果
    cpuMonitor.clear();
    
    // 發現 API 端點
    const endpoints = await this.discoverAPIEndpoints();
    console.log(`📡 發現 ${endpoints.length} 個 API 端點\n`);
    
    // 測試每個端點
    for (const endpoint of endpoints) {
      for (const method of endpoint.methods) {
        const result = await this.testAPIEndpoint(endpoint, method);
        if (result) {
          this.testResults.push(result);
        }
      }
    }
    
    // 執行關鍵功能測試
    await this.runKeyFunctionTests();
    
    // 生成報告
    const report = await this.generateReport();
    
    return report;
  }

  /**
   * 執行關鍵功能的專項測試
   */
  async runKeyFunctionTests() {
    console.log('\n🎯 執行關鍵功能測試...');
    
    const keyTests = [
      {
        name: 'PayUni-CreateOrder',
        fn: () => this.simulatePayUniCreateOrder()
      },
      {
        name: 'GoogleSheets-Write',
        fn: () => this.simulateGoogleSheetsWrite()
      },
      {
        name: 'GoogleSheets-Read',
        fn: () => this.simulateGoogleSheetsRead()
      },
      {
        name: 'FormValidation',
        fn: () => this.simulateFormValidation()
      },
      {
        name: 'EmailSending',
        fn: () => this.simulateEmailSending()
      }
    ];
    
    for (const test of keyTests) {
      try {
        await cpuMonitor.measureCPUTime(test.name, test.fn);
      } catch (error) {
        console.error(`❌ 關鍵功能測試失敗 ${test.name}:`, error.message);
      }
    }
  }

  /**
   * 模擬 PayUni 訂單建立
   */
  async simulatePayUniCreateOrder() {
    // 模擬 PayUni API 調用的計算密集操作
    const orderData = this.testData.paymentData;
    
    // 模擬加密計算
    const crypto = await import('crypto');
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(orderData));
    const orderHash = hash.digest('hex');
    
    // 模擬資料處理
    await new Promise(resolve => setTimeout(resolve, 1));
    
    return { orderId: orderHash.substring(0, 16), status: 'created' };
  }

  /**
   * 模擬 Google Sheets 寫入操作
   */
  async simulateGoogleSheetsWrite() {
    const data = this.testData.sheetsData;
    
    // 模擬資料序列化和驗證
    const serialized = JSON.stringify(data);
    const validated = JSON.parse(serialized);
    
    // 模擬網路延遲
    await new Promise(resolve => setTimeout(resolve, 2));
    
    return { rowsUpdated: validated.values.length };
  }

  /**
   * 模擬 Google Sheets 讀取操作
   */
  async simulateGoogleSheetsRead() {
    // 模擬資料解析
    const mockData = Array.from({ length: 100 }, (_, i) => ({
      id: i,
      name: `User ${i}`,
      email: `user${i}@example.com`
    }));
    
    // 模擬資料過濾和轉換
    const filtered = mockData.filter(item => item.id % 2 === 0);
    const transformed = filtered.map(item => ({
      ...item,
      displayName: `${item.name} <${item.email}>`
    }));
    
    return { count: transformed.length, data: transformed };
  }

  /**
   * 模擬表單驗證
   */
  async simulateFormValidation() {
    const formData = this.testData.contactForm;
    
    // 模擬複雜的驗證邏輯
    const validations = [
      () => formData.email.includes('@'),
      () => formData.phone.match(/^09\d{8}$/),
      () => formData.name.length > 0,
      () => formData.message.length < 1000
    ];
    
    const results = validations.map(validation => validation());
    const isValid = results.every(result => result);
    
    return { isValid, validations: results };
  }

  /**
   * 模擬郵件發送
   */
  async simulateEmailSending() {
    const emailData = {
      to: this.testData.contactForm.email,
      subject: '測試郵件',
      body: '這是一封測試郵件'
    };
    
    // 模擬郵件模板渲染
    const template = `
      <html>
        <body>
          <h1>${emailData.subject}</h1>
          <p>${emailData.body}</p>
        </body>
      </html>
    `;
    
    // 模擬郵件發送處理
    await new Promise(resolve => setTimeout(resolve, 1));
    
    return { sent: true, messageId: `msg_${Date.now()}` };
  }

  /**
   * 生成測試報告（使用固定檔名）
   */
  async generateReport() {
    const cpuReport = cpuMonitor.getReport();
    const timestamp = new Date().toISOString();

    const report = {
      timestamp,
      summary: cpuReport.summary,
      testResults: this.testResults,
      cpuMeasurements: cpuReport.details,
      recommendations: cpuReport.recommendations,
      passed: cpuMonitor.isPassing()
    };

    // 使用固定檔名儲存報告
    const reportPath = 'cpu-test-report.json';

    // 備份現有報告
    await this.backupExistingReport(reportPath);

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    console.log(`📊 CPU 測試報告已儲存至: ${reportPath}`);

    // 顯示摘要
    this.displaySummary(report);

    return report;
  }

  /**
   * 備份現有報告並管理歷史檔案
   */
  async backupExistingReport(reportPath) {
    try {
      // 檢查檔案是否存在
      await fs.access(reportPath);

      // 建立備份檔名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = reportPath.replace('.json', `-backup-${timestamp}.json`);

      // 複製現有報告為備份
      await fs.copyFile(reportPath, backupPath);

      // 清理舊的備份檔案（保留最近 5 份）
      await this.cleanupOldBackups('.', 'cpu-test-report', 5);

    } catch (error) {
      // 檔案不存在，無需備份
    }
  }

  /**
   * 清理舊的備份檔案
   */
  async cleanupOldBackups(dir, baseName, maxBackups = 5) {
    try {
      const files = await fs.readdir(dir);
      const backupFiles = files
        .filter(file => file.startsWith(`${baseName}-backup-`) && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(dir, file),
          time: fs.stat(path.join(dir, file)).then(stats => stats.mtime)
        }));

      // 等待所有檔案時間資訊
      for (const file of backupFiles) {
        file.time = await file.time;
      }

      // 按時間排序（最新的在前）
      backupFiles.sort((a, b) => b.time - a.time);

      // 刪除超過限制的舊檔案
      const filesToDelete = backupFiles.slice(maxBackups);
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
        console.log(`🗑️ 已清理舊備份: ${file.name}`);
      }
    } catch (error) {
      console.warn('清理備份檔案時發生錯誤:', error.message);
    }
  }

  /**
   * 顯示測試摘要
   */
  displaySummary(report) {
    console.log('\n📊 CPU 限制測試報告');
    console.log('='.repeat(50));
    console.log(`測試時間: ${report.timestamp}`);
    console.log(`總測試數: ${report.summary.total}`);
    console.log(`通過數量: ${report.summary.passed} ✅`);
    console.log(`警告數量: ${report.summary.warnings} ⚠️`);
    console.log(`超限數量: ${report.summary.exceeded} ❌`);
    console.log(`通過率: ${report.summary.passRate}%`);
    console.log(`平均 CPU 時間: ${report.summary.avgCPUTime}ms`);
    console.log(`最大 CPU 時間: ${report.summary.maxCPUTime}ms`);
    console.log(`整體結果: ${report.passed ? '✅ 通過' : '❌ 失敗'}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 優化建議:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. [${rec.type}] ${rec.message}`);
        rec.suggestions.forEach(suggestion => {
          console.log(`   - ${suggestion}`);
        });
      });
    }
    
    console.log('\n='.repeat(50));
  }
}

// 如果直接執行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new CPULimitTester();
  
  tester.runAllTests()
    .then(report => {
      process.exit(report.passed ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ 測試執行失敗:', error);
      process.exit(1);
    });
}

export { CPULimitTester };
