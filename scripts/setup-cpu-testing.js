#!/usr/bin/env node

/**
 * CPU 限制測試環境快速設定腳本
 * 自動配置測試環境並執行初始檢查
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

class CPUTestingSetup {
  constructor() {
    this.setupSteps = [
      { name: '檢查環境', fn: () => this.checkEnvironment() },
      { name: '設定環境變數', fn: () => this.setupEnvironmentVariables() },
      { name: '驗證依賴', fn: () => this.verifyDependencies() },
      { name: '執行初始測試', fn: () => this.runInitialTests() },
      { name: '生成設定報告', fn: () => this.generateSetupReport() }
    ];
    
    this.results = [];
  }

  /**
   * 執行完整設定流程
   */
  async setup() {
    console.log('🚀 開始設定 CPU 限制測試環境...\n');
    
    for (const step of this.setupSteps) {
      try {
        console.log(`📋 ${step.name}...`);
        const result = await step.fn();
        this.results.push({
          step: step.name,
          success: true,
          result
        });
        console.log(`✅ ${step.name} 完成\n`);
      } catch (error) {
        console.error(`❌ ${step.name} 失敗:`, error.message);
        this.results.push({
          step: step.name,
          success: false,
          error: error.message
        });
        
        // 詢問是否繼續
        const shouldContinue = await this.askContinue();
        if (!shouldContinue) {
          break;
        }
      }
    }
    
    this.displaySummary();
  }

  /**
   * 檢查環境
   */
  async checkEnvironment() {
    const checks = [];
    
    // 檢查 Node.js 版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    checks.push({
      name: 'Node.js 版本',
      status: majorVersion >= 18 ? 'OK' : 'WARNING',
      value: nodeVersion,
      message: majorVersion >= 18 ? '版本符合要求' : '建議使用 Node.js 18 或更高版本'
    });
    
    // 檢查專案結構
    const requiredDirs = ['src', 'lib', 'scripts'];
    for (const dir of requiredDirs) {
      const dirPath = path.join(projectRoot, dir);
      try {
        await fs.access(dirPath);
        checks.push({
          name: `目錄 ${dir}`,
          status: 'OK',
          value: '存在',
          message: '目錄結構正確'
        });
      } catch {
        checks.push({
          name: `目錄 ${dir}`,
          status: 'ERROR',
          value: '不存在',
          message: `缺少必要目錄: ${dir}`
        });
      }
    }
    
    // 檢查必要檔案
    const requiredFiles = [
      'lib/cpu-time-monitor.js',
      'scripts/test-cpu-limits.js',
      'scripts/benchmark-key-functions.js'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(projectRoot, file);
      try {
        await fs.access(filePath);
        checks.push({
          name: `檔案 ${file}`,
          status: 'OK',
          value: '存在',
          message: '測試工具已就緒'
        });
      } catch {
        checks.push({
          name: `檔案 ${file}`,
          status: 'ERROR',
          value: '不存在',
          message: `缺少必要檔案: ${file}`
        });
      }
    }
    
    return checks;
  }

  /**
   * 設定環境變數
   */
  async setupEnvironmentVariables() {
    const envLocalPath = path.join(projectRoot, '.env.local');
    const envExamplePath = path.join(projectRoot, '.env.example');
    
    // 檢查 .env.local 是否存在
    let envExists = false;
    try {
      await fs.access(envLocalPath);
      envExists = true;
    } catch {
      // 檔案不存在
    }
    
    if (!envExists) {
      // 嘗試從 .env.example 複製
      try {
        await fs.access(envExamplePath);
        await fs.copyFile(envExamplePath, envLocalPath);
        console.log('📄 已從 .env.example 建立 .env.local');
      } catch {
        // 建立基本的 .env.local
        const basicEnv = `# CPU 測試環境變數
NODE_ENV=test
APP_ENVIRONMENT=sandbox

# 測試用的環境變數（請替換為實際值）
GOOGLE_SHEETS_PRIVATE_KEY="your-private-key"
GOOGLE_SHEETS_CLIENT_EMAIL="<EMAIL>"
PAYUNI_HASH_KEY="your-hash-key"
PAYUNI_HASH_IV="your-hash-iv"
PAYUNI_MERCHANT_ID="your-merchant-id"
`;
        await fs.writeFile(envLocalPath, basicEnv);
        console.log('📄 已建立基本的 .env.local 檔案');
      }
    }
    
    // 檢查測試相關環境變數
    const testEnvVars = [
      'NODE_ENV=test',
      'APP_ENVIRONMENT=sandbox'
    ];
    
    let envContent = await fs.readFile(envLocalPath, 'utf8');
    let modified = false;
    
    for (const envVar of testEnvVars) {
      const [key] = envVar.split('=');
      if (!envContent.includes(key)) {
        envContent += `\n${envVar}`;
        modified = true;
      }
    }
    
    if (modified) {
      await fs.writeFile(envLocalPath, envContent);
      console.log('📝 已更新 .env.local 檔案');
    }
    
    return {
      envFileExists: true,
      testVarsAdded: modified,
      path: envLocalPath
    };
  }

  /**
   * 驗證依賴
   */
  async verifyDependencies() {
    const packageJsonPath = path.join(projectRoot, 'package.json');
    
    try {
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
      
      // 檢查必要的 scripts
      const requiredScripts = [
        'test:cpu',
        'benchmark',
        'cpu-check'
      ];
      
      const missingScripts = requiredScripts.filter(script => 
        !packageJson.scripts || !packageJson.scripts[script]
      );
      
      if (missingScripts.length > 0) {
        console.log('⚠️ 缺少必要的 npm scripts，正在添加...');
        
        if (!packageJson.scripts) {
          packageJson.scripts = {};
        }
        
        // 添加缺少的 scripts
        const scriptsToAdd = {
          'test:cpu': 'node scripts/test-cpu-limits.js',
          'benchmark': 'node scripts/benchmark-key-functions.js',
          'cpu-check': 'npm run test:cpu && npm run benchmark',
          'pre-deploy': 'npm run lint && npm run test && npm run cpu-check'
        };
        
        missingScripts.forEach(script => {
          if (scriptsToAdd[script]) {
            packageJson.scripts[script] = scriptsToAdd[script];
          }
        });
        
        // 寫回 package.json
        await fs.writeFile(
          packageJsonPath, 
          JSON.stringify(packageJson, null, 2) + '\n'
        );
        
        console.log('✅ 已添加必要的 npm scripts');
      }
      
      return {
        scriptsOK: missingScripts.length === 0,
        addedScripts: missingScripts,
        totalScripts: Object.keys(packageJson.scripts).length
      };
      
    } catch (error) {
      throw new Error(`無法讀取或更新 package.json: ${error.message}`);
    }
  }

  /**
   * 執行初始測試
   */
  async runInitialTests() {
    const tests = [];
    
    // 測試 CPU 監控工具
    try {
      console.log('🧪 測試 CPU 監控工具...');
      const { cpuMonitor } = await import('../lib/cpu-time-monitor.js');
      
      // 執行簡單測試
      const testResult = await cpuMonitor.measureCPUTime('SetupTest', async () => {
        // 模擬一些計算
        let sum = 0;
        for (let i = 0; i < 1000; i++) {
          sum += Math.random();
        }
        return sum;
      });
      
      tests.push({
        name: 'CPU 監控工具',
        status: 'OK',
        result: `測試完成，CPU 時間: ${cpuMonitor.results[0]?.cpuTime?.toFixed(2)}ms`
      });
      
    } catch (error) {
      tests.push({
        name: 'CPU 監控工具',
        status: 'ERROR',
        error: error.message
      });
    }
    
    // 測試優化策略
    try {
      console.log('🔧 測試優化策略...');
      const { OptimizedFormValidator } = await import('../lib/optimization-strategies.js');
      
      const validator = new OptimizedFormValidator();
      const testForm = {
        name: '測試用戶',
        email: '<EMAIL>',
        phone: '0912345678'
      };
      
      const validation = validator.validateForm(testForm);
      
      tests.push({
        name: '優化策略',
        status: validation.isValid ? 'OK' : 'WARNING',
        result: `表單驗證測試: ${validation.isValid ? '通過' : '失敗'}`
      });
      
    } catch (error) {
      tests.push({
        name: '優化策略',
        status: 'ERROR',
        error: error.message
      });
    }
    
    return tests;
  }

  /**
   * 生成設定報告
   */
  async generateSetupReport() {
    const report = {
      timestamp: new Date().toISOString(),
      setupResults: this.results,
      summary: {
        totalSteps: this.results.length,
        successful: this.results.filter(r => r.success).length,
        failed: this.results.filter(r => !r.success).length
      },
      nextSteps: this.generateNextSteps()
    };
    
    const reportPath = path.join(projectRoot, 'cpu-testing-setup-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📊 設定報告已儲存至: ${reportPath}`);
    
    return report;
  }

  /**
   * 生成後續步驟建議
   */
  generateNextSteps() {
    const steps = [];
    
    const failedSteps = this.results.filter(r => !r.success);
    
    if (failedSteps.length === 0) {
      steps.push('✅ 設定完成！可以開始使用 CPU 限制測試工具');
      steps.push('🧪 執行: npm run cpu-check');
      steps.push('📚 查看指南: docs/cpu-limit-testing-guide.md');
    } else {
      steps.push('❌ 有設定步驟失敗，請檢查錯誤訊息');
      failedSteps.forEach(step => {
        steps.push(`- 修復: ${step.step} (${step.error})`);
      });
    }
    
    steps.push('🔧 設定 GitHub Secrets 以啟用 CI/CD 測試');
    steps.push('📖 閱讀完整文件: docs/cpu-limit-testing-guide.md');
    
    return steps;
  }

  /**
   * 詢問是否繼續
   */
  async askContinue() {
    // 在實際環境中，這裡可以使用 readline 來詢問用戶
    // 為了簡化，這裡直接返回 true
    console.log('⚠️ 發生錯誤，但繼續執行其他步驟...\n');
    return true;
  }

  /**
   * 顯示設定摘要
   */
  displaySummary() {
    console.log('\n📋 CPU 限制測試環境設定摘要');
    console.log('='.repeat(50));
    
    const successful = this.results.filter(r => r.success).length;
    const total = this.results.length;
    
    console.log(`設定步驟: ${successful}/${total} 成功`);
    
    this.results.forEach(result => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} ${result.step}`);
      if (!result.success) {
        console.log(`   錯誤: ${result.error}`);
      }
    });
    
    console.log('\n🎯 後續步驟:');
    const nextSteps = this.generateNextSteps();
    nextSteps.forEach(step => {
      console.log(`  ${step}`);
    });
    
    console.log('\n='.repeat(50));
    
    if (successful === total) {
      console.log('🎉 設定完成！現在可以開始使用 CPU 限制測試工具了。');
      console.log('\n快速開始:');
      console.log('  npm run cpu-check');
    } else {
      console.log('⚠️ 設定未完全成功，請檢查上述錯誤並重新執行。');
    }
  }
}

// 如果直接執行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new CPUTestingSetup();
  
  setup.setup()
    .then(() => {
      console.log('\n設定流程完成。');
    })
    .catch(error => {
      console.error('\n❌ 設定流程失敗:', error);
      process.exit(1);
    });
}

export { CPUTestingSetup };
