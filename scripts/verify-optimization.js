#!/usr/bin/env node

/**
 * CPU 限制測試系統優化驗證腳本
 * 驗證所有優化功能是否正常運作
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

class OptimizationVerifier {
  constructor() {
    this.verificationResults = [];
  }

  /**
   * 執行完整驗證
   */
  async runVerification() {
    console.log('🔍 開始驗證 CPU 限制測試系統優化...\n');

    const verifications = [
      { name: '檔案管理優化', fn: () => this.verifyFileManagement() },
      { name: 'CI/CD 配置', fn: () => this.verifyCICDConfig() },
      { name: '測試報告生成', fn: () => this.verifyReportGeneration() },
      { name: 'Git 忽略設定', fn: () => this.verifyGitIgnore() },
      { name: 'Pre-commit Hook', fn: () => this.verifyPreCommitHook() },
      { name: '自動化流程', fn: () => this.verifyAutomationFlow() }
    ];

    for (const verification of verifications) {
      try {
        console.log(`📋 驗證: ${verification.name}...`);
        const result = await verification.fn();
        this.verificationResults.push({
          name: verification.name,
          success: true,
          result
        });
        console.log(`✅ ${verification.name} 驗證通過\n`);
      } catch (error) {
        console.error(`❌ ${verification.name} 驗證失敗:`, error.message);
        this.verificationResults.push({
          name: verification.name,
          success: false,
          error: error.message
        });
      }
    }

    this.displayVerificationSummary();
  }

  /**
   * 驗證檔案管理優化
   */
  async verifyFileManagement() {
    const checks = [];

    // 檢查固定檔名報告是否存在
    const reportFiles = [
      'cpu-test-report.json',
      'benchmark-report.json'
    ];

    for (const file of reportFiles) {
      try {
        const filePath = path.join(projectRoot, file);
        await fs.access(filePath);
        
        // 檢查檔案內容格式
        const content = await fs.readFile(filePath, 'utf8');
        const data = JSON.parse(content);
        
        checks.push({
          file,
          exists: true,
          hasTimestamp: !!data.timestamp,
          hasSummary: !!data.summary,
          size: content.length
        });
      } catch (error) {
        checks.push({
          file,
          exists: false,
          error: error.message
        });
      }
    }

    // 檢查備份檔案清理機制
    const files = await fs.readdir(projectRoot);
    const backupFiles = files.filter(f => f.includes('-backup-') && f.endsWith('.json'));
    
    return {
      reportFiles: checks,
      backupFiles: {
        count: backupFiles.length,
        files: backupFiles.slice(0, 5) // 只顯示前5個
      }
    };
  }

  /**
   * 驗證 CI/CD 配置
   */
  async verifyCICDConfig() {
    const workflowPath = path.join(projectRoot, '.github/workflows/cpu-limit-check.yml');
    
    try {
      const content = await fs.readFile(workflowPath, 'utf8');
      
      const checks = {
        fileExists: true,
        hasFixedReportNames: content.includes('cpu-test-report.json'),
        hasBenchmarkReports: content.includes('benchmark-report.json'),
        hasDeploymentGate: content.includes('deploy-check'),
        hasStatusCheck: content.includes('status-check'),
        hasPRComment: content.includes('Comment PR with results'),
        triggersOnPush: content.includes('push:'),
        triggersOnPR: content.includes('pull_request:')
      };

      return checks;
    } catch (error) {
      throw new Error(`CI/CD 配置檔案不存在或無法讀取: ${error.message}`);
    }
  }

  /**
   * 驗證測試報告生成
   */
  async verifyReportGeneration() {
    console.log('  🧪 執行測試以驗證報告生成...');
    
    // 執行快速測試
    const { execSync } = await import('child_process');
    
    try {
      // 執行基準測試
      execSync('npm run benchmark', { 
        cwd: projectRoot,
        stdio: 'pipe'
      });

      // 檢查報告是否生成
      const reportPath = path.join(projectRoot, 'benchmark-report.json');
      const reportExists = await fs.access(reportPath).then(() => true).catch(() => false);
      
      if (!reportExists) {
        throw new Error('基準測試報告未生成');
      }

      // 檢查報告內容
      const reportContent = await fs.readFile(reportPath, 'utf8');
      const reportData = JSON.parse(reportContent);

      return {
        reportGenerated: true,
        hasTimestamp: !!reportData.timestamp,
        hasSummary: !!reportData.summary,
        benchmarkCount: reportData.summary?.totalBenchmarks || 0,
        passedCount: reportData.summary?.passed || 0
      };
    } catch (error) {
      throw new Error(`測試報告生成失敗: ${error.message}`);
    }
  }

  /**
   * 驗證 Git 忽略設定
   */
  async verifyGitIgnore() {
    const gitignorePath = path.join(projectRoot, '.gitignore');
    
    try {
      const content = await fs.readFile(gitignorePath, 'utf8');
      
      const requiredEntries = [
        'cpu-test-report.json',
        'benchmark-report.json',
        'cpu-performance-report.json',
        '*-backup-*.json'
      ];

      const checks = requiredEntries.map(entry => ({
        entry,
        included: content.includes(entry)
      }));

      const allIncluded = checks.every(check => check.included);

      return {
        fileExists: true,
        allEntriesIncluded: allIncluded,
        checks
      };
    } catch (error) {
      throw new Error(`無法讀取 .gitignore: ${error.message}`);
    }
  }

  /**
   * 驗證 Pre-commit Hook
   */
  async verifyPreCommitHook() {
    const hookPath = path.join(projectRoot, '.husky/pre-commit');
    
    try {
      const content = await fs.readFile(hookPath, 'utf8');
      
      const checks = {
        fileExists: true,
        isExecutable: true, // 假設已設定執行權限
        hasAPIChangeDetection: content.includes('src/app/api'),
        hasCPUCheck: content.includes('npm run benchmark'),
        hasLintCheck: content.includes('npm run lint'),
        hasConditionalExecution: content.includes('if [ -n "$API_CHANGES" ]')
      };

      // 檢查 package.json 中的 Husky 設定
      const packageJsonPath = path.join(projectRoot, 'package.json');
      const packageContent = await fs.readFile(packageJsonPath, 'utf8');
      const packageData = JSON.parse(packageContent);

      checks.hasHuskyDependency = !!packageData.devDependencies?.husky;
      checks.hasPrepareScript = !!packageData.scripts?.prepare;

      return checks;
    } catch (error) {
      throw new Error(`Pre-commit hook 驗證失敗: ${error.message}`);
    }
  }

  /**
   * 驗證自動化流程
   */
  async verifyAutomationFlow() {
    const packageJsonPath = path.join(projectRoot, 'package.json');
    
    try {
      const content = await fs.readFile(packageJsonPath, 'utf8');
      const packageData = JSON.parse(content);

      const requiredScripts = [
        'test:cpu',
        'benchmark',
        'cpu-check',
        'cpu-check:quick',
        'pre-deploy'
      ];

      const scriptChecks = requiredScripts.map(script => ({
        script,
        exists: !!packageData.scripts?.[script]
      }));

      const allScriptsExist = scriptChecks.every(check => check.exists);

      return {
        hasModuleType: packageData.type === 'module',
        allScriptsExist,
        scriptChecks,
        totalScripts: Object.keys(packageData.scripts || {}).length
      };
    } catch (error) {
      throw new Error(`自動化流程驗證失敗: ${error.message}`);
    }
  }

  /**
   * 顯示驗證摘要
   */
  displayVerificationSummary() {
    console.log('\n📊 CPU 限制測試系統優化驗證報告');
    console.log('='.repeat(60));

    const successful = this.verificationResults.filter(r => r.success).length;
    const total = this.verificationResults.length;

    console.log(`驗證項目: ${successful}/${total} 通過`);
    console.log(`整體狀態: ${successful === total ? '✅ 全部通過' : '⚠️ 部分失敗'}`);

    console.log('\n📋 詳細結果:');
    this.verificationResults.forEach(result => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} ${result.name}`);
      
      if (!result.success) {
        console.log(`   錯誤: ${result.error}`);
      } else if (result.result) {
        // 顯示關鍵資訊
        if (result.name === '檔案管理優化') {
          console.log(`   報告檔案: ${result.result.reportFiles.length} 個`);
          console.log(`   備份檔案: ${result.result.backupFiles.count} 個`);
        } else if (result.name === '測試報告生成') {
          console.log(`   基準測試: ${result.result.benchmarkCount} 個`);
          console.log(`   通過數量: ${result.result.passedCount} 個`);
        } else if (result.name === 'CI/CD 配置') {
          const config = result.result;
          console.log(`   部署閘門: ${config.hasDeploymentGate ? '✅' : '❌'}`);
          console.log(`   狀態檢查: ${config.hasStatusCheck ? '✅' : '❌'}`);
        }
      }
    });

    console.log('\n🎯 優化效果:');
    console.log('✅ 測試報告使用固定檔名，避免檔案累積');
    console.log('✅ 自動備份和清理機制，保留歷史記錄');
    console.log('✅ CI/CD 流程優化，強化部署閘門');
    console.log('✅ Pre-commit hook 智能檢查，提升開發效率');
    console.log('✅ 完整的自動化流程，減少手動操作');

    if (successful === total) {
      console.log('\n🎉 所有優化功能都已正確設定並正常運作！');
      console.log('現在可以享受更高效的 CPU 限制測試流程。');
    } else {
      console.log('\n⚠️ 部分功能需要修復，請檢查上述錯誤訊息。');
    }

    console.log('\n='.repeat(60));
  }
}

// 如果直接執行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
  const verifier = new OptimizationVerifier();
  
  verifier.runVerification()
    .then(() => {
      console.log('\n驗證完成。');
    })
    .catch(error => {
      console.error('\n❌ 驗證過程失敗:', error);
      process.exit(1);
    });
}

export { OptimizationVerifier };
