/**
 * Cloudflare Pages CPU 時間監控工具
 * 模擬 10ms CPU 限制並提供詳細的效能分析
 */

import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';

export class CPUTimeMonitor {
  constructor(options = {}) {
    this.cpuLimit = options.cpuLimit || 10; // 10ms 限制
    this.warningThreshold = options.warningThreshold || 8; // 8ms 警告閾值
    this.results = [];
    this.isEnabled = process.env.NODE_ENV !== 'production';
  }

  /**
   * 測量函數的 CPU 執行時間
   */
  async measureCPUTime(functionName, fn, ...args) {
    if (!this.isEnabled) {
      return await fn(...args);
    }

    const startTime = performance.now();
    const startCPU = process.cpuUsage();
    
    let result;
    let error = null;
    
    try {
      result = await fn(...args);
    } catch (err) {
      error = err;
    }
    
    const endTime = performance.now();
    const endCPU = process.cpuUsage(startCPU);
    
    // 計算實際 CPU 時間 (微秒轉毫秒)
    const cpuTimeMs = (endCPU.user + endCPU.system) / 1000;
    const wallTimeMs = endTime - startTime;
    
    const measurement = {
      functionName,
      cpuTime: cpuTimeMs,
      wallTime: wallTimeMs,
      timestamp: new Date().toISOString(),
      status: this.getStatus(cpuTimeMs),
      error: error?.message || null,
      args: this.sanitizeArgs(args)
    };
    
    this.results.push(measurement);
    this.logResult(measurement);
    
    if (error) {
      throw error;
    }
    
    return result;
  }

  /**
   * 包裝 API 處理函數
   */
  wrapAPIHandler(handlerName, handler) {
    return async (req, res) => {
      return this.measureCPUTime(handlerName, handler, req, res);
    };
  }

  /**
   * 包裝 Next.js App Router API 函數
   */
  wrapAppRouterAPI(routeName, handler) {
    return async (request, context) => {
      return this.measureCPUTime(routeName, handler, request, context);
    };
  }

  /**
   * 獲取狀態標籤
   */
  getStatus(cpuTime) {
    if (cpuTime > this.cpuLimit) {
      return 'EXCEEDED';
    } else if (cpuTime > this.warningThreshold) {
      return 'WARNING';
    }
    return 'OK';
  }

  /**
   * 清理敏感參數
   */
  sanitizeArgs(args) {
    return args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        const sanitized = { ...arg };
        // 移除敏感資訊
        delete sanitized.PAYUNI_HASH_KEY;
        delete sanitized.PAYUNI_HASH_IV;
        delete sanitized.GOOGLE_SHEETS_PRIVATE_KEY;
        return sanitized;
      }
      return arg;
    });
  }

  /**
   * 記錄測量結果
   */
  logResult(measurement) {
    const { functionName, cpuTime, wallTime, status } = measurement;
    const statusIcon = {
      'OK': '✅',
      'WARNING': '⚠️',
      'EXCEEDED': '❌'
    }[status];
    
    console.log(
      `${statusIcon} [CPU Monitor] ${functionName}: ` +
      `CPU=${cpuTime.toFixed(2)}ms, Wall=${wallTime.toFixed(2)}ms, ` +
      `Status=${status}`
    );
    
    if (status === 'EXCEEDED') {
      console.error(
        `🚨 CPU 限制超標: ${functionName} 使用了 ${cpuTime.toFixed(2)}ms ` +
        `(限制: ${this.cpuLimit}ms)`
      );
    }
  }

  /**
   * 獲取測試報告
   */
  getReport() {
    const total = this.results.length;
    const exceeded = this.results.filter(r => r.status === 'EXCEEDED').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const passed = this.results.filter(r => r.status === 'OK').length;
    
    const avgCPUTime = this.results.reduce((sum, r) => sum + r.cpuTime, 0) / total;
    const maxCPUTime = Math.max(...this.results.map(r => r.cpuTime));
    
    return {
      summary: {
        total,
        passed,
        warnings,
        exceeded,
        passRate: ((passed / total) * 100).toFixed(1),
        avgCPUTime: avgCPUTime.toFixed(2),
        maxCPUTime: maxCPUTime.toFixed(2)
      },
      details: this.results,
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 生成優化建議
   */
  generateRecommendations() {
    const exceededFunctions = this.results.filter(r => r.status === 'EXCEEDED');
    const warningFunctions = this.results.filter(r => r.status === 'WARNING');
    
    const recommendations = [];
    
    if (exceededFunctions.length > 0) {
      recommendations.push({
        type: 'CRITICAL',
        message: `${exceededFunctions.length} 個函數超過 CPU 限制，必須優化`,
        functions: exceededFunctions.map(f => f.functionName),
        suggestions: [
          '減少同步計算操作',
          '使用快取減少重複計算',
          '優化資料庫查詢',
          '考慮將複雜邏輯移至客戶端'
        ]
      });
    }
    
    if (warningFunctions.length > 0) {
      recommendations.push({
        type: 'WARNING',
        message: `${warningFunctions.length} 個函數接近 CPU 限制，建議優化`,
        functions: warningFunctions.map(f => f.functionName),
        suggestions: [
          '監控效能趨勢',
          '預先優化以防止未來超限',
          '考慮使用非同步處理'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * 儲存報告到檔案（使用固定檔名）
   */
  async saveReport(filePath = 'cpu-performance-report.json') {
    const report = this.getReport();
    const reportPath = path.resolve(filePath);

    // 備份現有報告
    await this.backupExistingReport(reportPath);

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    console.log(`📊 CPU 效能報告已儲存至: ${reportPath}`);

    return reportPath;
  }

  /**
   * 備份現有報告並管理歷史檔案
   */
  async backupExistingReport(reportPath) {
    try {
      // 檢查檔案是否存在
      await fs.access(reportPath);

      // 建立備份檔名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = reportPath.replace('.json', `-backup-${timestamp}.json`);

      // 複製現有報告為備份
      await fs.copyFile(reportPath, backupPath);

      // 清理舊的備份檔案（保留最近 3 份）
      await this.cleanupOldBackups(path.dirname(reportPath), path.basename(reportPath, '.json'));

    } catch {
      // 檔案不存在，無需備份
    }
  }

  /**
   * 清理舊的備份檔案
   */
  async cleanupOldBackups(dir, baseName, maxBackups = 3) {
    try {
      const files = await fs.readdir(dir);
      const backupFiles = files
        .filter(file => file.startsWith(`${baseName}-backup-`) && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(dir, file),
          time: fs.stat(path.join(dir, file)).then(stats => stats.mtime)
        }));

      // 等待所有檔案時間資訊
      for (const file of backupFiles) {
        file.time = await file.time;
      }

      // 按時間排序（最新的在前）
      backupFiles.sort((a, b) => b.time - a.time);

      // 刪除超過限制的舊檔案
      const filesToDelete = backupFiles.slice(maxBackups);
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
        console.log(`🗑️ 已清理舊備份: ${file.name}`);
      }
    } catch (error) {
      console.warn('清理備份檔案時發生錯誤:', error.message);
    }
  }

  /**
   * 清除測試結果
   */
  clear() {
    this.results = [];
  }

  /**
   * 檢查是否通過 CPU 限制測試
   */
  isPassing() {
    return this.results.every(r => r.status !== 'EXCEEDED');
  }
}

// 全域監控實例
export const cpuMonitor = new CPUTimeMonitor();

/**
 * 裝飾器函數，用於自動監控 API 函數
 */
export function monitorCPU(functionName) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      return cpuMonitor.measureCPUTime(
        functionName || `${target.constructor.name}.${propertyKey}`,
        originalMethod.bind(this),
        ...args
      );
    };
    
    return descriptor;
  };
}

/**
 * 高階函數包裝器
 */
export function withCPUMonitoring(functionName, fn) {
  return async (...args) => {
    return cpuMonitor.measureCPUTime(functionName, fn, ...args);
  };
}
