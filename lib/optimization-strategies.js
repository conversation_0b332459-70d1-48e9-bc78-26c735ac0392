/**
 * Cloudflare Pages 10ms CPU 限制優化策略
 * 提供具體的程式碼優化建議和實作範例
 */

/**
 * 快取管理器 - 減少重複計算
 */
export class CacheManager {
  constructor(maxSize = 100, ttl = 300000) { // 5分鐘 TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  set(key, value) {
    // 清理過期項目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  clear() {
    this.cache.clear();
  }
}

// 全域快取實例
export const globalCache = new CacheManager();

/**
 * 優化策略 1: 表單驗證優化
 */
export class OptimizedFormValidator {
  constructor() {
    // 預編譯正則表達式
    this.emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    this.phoneRegex = /^(09\d{8}|886\d{9})$/;
    this.nameRegex = /^[\u4e00-\u9fa5a-zA-Z\s]{1,50}$/;
  }

  // 快速驗證 - 避免複雜的字串操作
  validateForm(formData) {
    const errors = [];
    
    // 使用預編譯的正則表達式
    if (!this.emailRegex.test(formData.email)) {
      errors.push('email');
    }
    
    // 快速電話號碼清理和驗證
    const cleanPhone = formData.phone.replace(/\D/g, '');
    if (!this.phoneRegex.test(cleanPhone)) {
      errors.push('phone');
    }
    
    // 簡化名稱驗證
    if (!formData.name || formData.name.length === 0 || formData.name.length > 50) {
      errors.push('name');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 批次驗證 - 一次處理多個表單
  validateBatch(forms) {
    return forms.map(form => ({
      ...form,
      validation: this.validateForm(form)
    }));
  }
}

/**
 * 優化策略 2: PayUni 加密優化
 */
export class OptimizedPayUniCrypto {
  constructor() {
    // 預先載入 crypto 模組
    this.crypto = null;
    this.initCrypto();
  }

  async initCrypto() {
    if (!this.crypto) {
      this.crypto = await import('crypto');
    }
  }

  // 優化的訂單建立 - 減少記憶體分配
  async createOrderFast(orderData) {
    await this.initCrypto();
    
    // 使用快取避免重複序列化
    const cacheKey = `order_${orderData.orderNumber}`;
    let serialized = globalCache.get(cacheKey);
    
    if (!serialized) {
      // 最小化的資料結構
      const minimalData = {
        o: orderData.orderNumber,
        a: orderData.amount,
        e: orderData.customerEmail,
        t: Date.now()
      };
      
      serialized = JSON.stringify(minimalData);
      globalCache.set(cacheKey, serialized);
    }
    
    // 快速雜湊計算
    const hash = this.crypto.createHash('sha256');
    hash.update(serialized);
    
    return {
      orderId: orderData.orderNumber,
      hash: hash.digest('hex').substring(0, 16), // 只取前16位
      timestamp: Date.now()
    };
  }

  // 批次處理多個訂單
  async createOrdersBatch(orders) {
    await this.initCrypto();
    
    const results = [];
    const hash = this.crypto.createHash('sha256');
    
    // 一次性處理所有訂單
    for (const order of orders) {
      const minimalData = {
        o: order.orderNumber,
        a: order.amount,
        t: Date.now()
      };
      
      hash.update(JSON.stringify(minimalData));
      results.push({
        orderId: order.orderNumber,
        processed: true
      });
    }
    
    const batchHash = hash.digest('hex');
    
    return {
      batchId: batchHash.substring(0, 16),
      orders: results,
      count: orders.length
    };
  }
}

/**
 * 優化策略 3: Google Sheets 操作優化
 */
export class OptimizedSheetsOperations {
  constructor() {
    this.batchSize = 50; // 批次處理大小
    this.writeBuffer = [];
  }

  // 優化的資料寫入 - 批次處理
  async writeDataOptimized(data) {
    // 簡化資料格式
    const simplified = this.simplifyData(data);
    
    // 添加到緩衝區
    this.writeBuffer.push(simplified);
    
    // 達到批次大小時才實際寫入
    if (this.writeBuffer.length >= this.batchSize) {
      return this.flushBuffer();
    }
    
    return { buffered: true, count: this.writeBuffer.length };
  }

  // 清空緩衝區並寫入
  async flushBuffer() {
    if (this.writeBuffer.length === 0) {
      return { written: 0 };
    }
    
    const batch = [...this.writeBuffer];
    this.writeBuffer = [];
    
    // 模擬批次寫入
    const result = {
      written: batch.length,
      timestamp: Date.now()
    };
    
    return result;
  }

  // 簡化資料結構
  simplifyData(data) {
    // 只保留必要欄位
    return {
      t: new Date().toISOString().substring(0, 19), // 去掉毫秒
      n: data.name?.substring(0, 50) || '', // 限制長度
      e: data.email?.toLowerCase() || '',
      s: data.status || 'pending'
    };
  }

  // 優化的資料讀取 - 增量讀取
  async readDataOptimized(range, lastTimestamp = null) {
    // 模擬增量讀取
    const mockData = this.generateMockData(100);
    
    // 如果有時間戳，只返回新資料
    let filteredData = mockData;
    if (lastTimestamp) {
      filteredData = mockData.filter(item => 
        new Date(item.timestamp) > new Date(lastTimestamp)
      );
    }
    
    // 只返回必要欄位
    const optimizedData = filteredData.map(item => ({
      id: item.id,
      status: item.status,
      amount: item.amount
    }));
    
    return {
      data: optimizedData,
      count: optimizedData.length,
      lastTimestamp: new Date().toISOString()
    };
  }

  generateMockData(count) {
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      timestamp: new Date(Date.now() - i * 60000).toISOString(),
      status: ['confirmed', 'pending', 'cancelled'][i % 3],
      amount: 1500 + (i % 5) * 500
    }));
  }
}

/**
 * 優化策略 4: 資料處理管道
 */
export class OptimizedDataPipeline {
  constructor() {
    this.processors = [];
  }

  // 添加處理器
  addProcessor(processor) {
    this.processors.push(processor);
    return this;
  }

  // 流式處理 - 避免大量記憶體使用
  async processStream(data) {
    let result = data;
    
    // 逐個應用處理器
    for (const processor of this.processors) {
      result = await processor(result);
      
      // 如果結果為空，提前退出
      if (!result || (Array.isArray(result) && result.length === 0)) {
        break;
      }
    }
    
    return result;
  }

  // 並行處理小批次
  async processBatch(dataArray, batchSize = 10) {
    const results = [];
    
    for (let i = 0; i < dataArray.length; i += batchSize) {
      const batch = dataArray.slice(i, i + batchSize);
      
      // 並行處理批次
      const batchResults = await Promise.all(
        batch.map(item => this.processStream(item))
      );
      
      results.push(...batchResults);
    }
    
    return results;
  }
}

/**
 * 優化策略 5: 記憶體管理
 */
export class MemoryOptimizer {
  constructor() {
    this.memoryThreshold = 100 * 1024 * 1024; // 100MB
  }

  // 檢查記憶體使用量
  checkMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return {
        used: usage.heapUsed,
        total: usage.heapTotal,
        percentage: (usage.heapUsed / usage.heapTotal) * 100
      };
    }
    return null;
  }

  // 強制垃圾回收（如果可用）
  forceGC() {
    if (global.gc) {
      global.gc();
      return true;
    }
    return false;
  }

  // 清理大型物件
  cleanupLargeObjects(...objects) {
    objects.forEach(obj => {
      if (obj && typeof obj === 'object') {
        // 清空陣列
        if (Array.isArray(obj)) {
          obj.length = 0;
        } else {
          // 清空物件屬性
          Object.keys(obj).forEach(key => {
            delete obj[key];
          });
        }
      }
    });
  }

  // 記憶體安全的資料處理
  async processWithMemoryLimit(data, processor, memoryLimit = this.memoryThreshold) {
    try {
      const result = await processor(data);

      const finalMemory = this.checkMemoryUsage();
      if (finalMemory && finalMemory.used > memoryLimit) {
        console.warn(`⚠️ 記憶體使用量過高: ${finalMemory.used / 1024 / 1024}MB`);
        this.forceGC();
      }

      return result;
    } catch (error) {
      // 發生錯誤時清理記憶體
      this.forceGC();
      throw error;
    }
  }
}

/**
 * 優化策略 6: 非同步操作優化
 */
export class AsyncOptimizer {
  constructor() {
    this.concurrencyLimit = 5;
  }

  // 限制並發數的 Promise.all
  async limitedParallel(tasks, limit = this.concurrencyLimit) {
    const results = [];
    
    for (let i = 0; i < tasks.length; i += limit) {
      const batch = tasks.slice(i, i + limit);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);
    }
    
    return results;
  }

  // 帶超時的非同步操作
  async withTimeout(promise, timeoutMs = 5000) {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Operation timeout')), timeoutMs);
    });
    
    return Promise.race([promise, timeoutPromise]);
  }

  // 重試機制
  async withRetry(fn, maxRetries = 3, delay = 100) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
      }
    }
    
    throw lastError;
  }
}

/**
 * 整合優化工具包
 */
export class OptimizationToolkit {
  constructor() {
    this.formValidator = new OptimizedFormValidator();
    this.payuniCrypto = new OptimizedPayUniCrypto();
    this.sheetsOps = new OptimizedSheetsOperations();
    this.dataPipeline = new OptimizedDataPipeline();
    this.memoryOptimizer = new MemoryOptimizer();
    this.asyncOptimizer = new AsyncOptimizer();
  }

  // 獲取所有優化工具
  getTools() {
    return {
      formValidator: this.formValidator,
      payuniCrypto: this.payuniCrypto,
      sheetsOps: this.sheetsOps,
      dataPipeline: this.dataPipeline,
      memoryOptimizer: this.memoryOptimizer,
      asyncOptimizer: this.asyncOptimizer,
      cache: globalCache
    };
  }

  // 應用所有優化策略到函數
  optimizeFunction(fn, options = {}) {
    return async (...args) => {
      const startTime = Date.now();
      
      try {
        // 執行函數
        const result = await this.asyncOptimizer.withTimeout(
          fn(...args),
          options.timeout || 8000 // 8秒超時，留2秒緩衝
        );

        // 記憶體清理
        const memoryAfter = this.memoryOptimizer.checkMemoryUsage();
        if (memoryAfter && memoryAfter.percentage > 80) {
          this.memoryOptimizer.forceGC();
        }
        
        const duration = Date.now() - startTime;
        if (duration > 8) {
          console.warn(`⚠️ 函數執行時間接近限制: ${duration}ms`);
        }
        
        return result;
      } catch (error) {
        // 錯誤時清理資源
        this.memoryOptimizer.forceGC();
        throw error;
      }
    };
  }
}

// 匯出預設實例
export const optimizationToolkit = new OptimizationToolkit();
