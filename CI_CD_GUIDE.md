# CI/CD 配置說明與使用指南

## 📋 目錄
- [概述](#概述)
- [GitHub Actions 工作流程](#github-actions-工作流程)
- [本地測試指令](#本地測試指令)
- [環境變數設定](#環境變數設定)
- [測試類型說明](#測試類型說明)
- [故障排除](#故障排除)

## 🎯 概述

本專案使用 GitHub Actions 進行持續整合 (CI)，包含以下自動化流程：
- **ESLint 程式碼檢查**
- **單元測試 (Unit Tests)**
- **整合測試 (Integration Tests)**
- **建置測試 (Build Test)**

## 🚀 GitHub Actions 工作流程

### 觸發條件
工作流程會在以下情況自動觸發：
- 推送到 `main` 或 `develop` 分支
- 對 `main` 或 `develop` 分支建立 Pull Request

### 工作流程結構
```yaml
# .github/workflows/test.yml
name: 自動化測試

jobs:
  unit-tests:      # 單元測試
  integration-tests: # 整合測試  
  build-test:      # 建置測試
```

### 各階段說明

#### 1. 單元測試 (unit-tests)
- **執行內容**：ESLint + 單元測試
- **測試範圍**：PayUni 工具函數、表單驗證邏輯
- **覆蓋率報告**：自動上傳到 Codecov

#### 2. 整合測試 (integration-tests)
- **執行內容**：API 路由測試、Google Sheets 整合
- **測試範圍**：付款流程、表單提交、資料庫操作
- **環境**：使用 MSW 模擬外部 API

#### 3. 建置測試 (build-test)
- **執行內容**：Next.js 生產建置
- **驗證**：確保所有頁面和 API 路由可正常建置

## 💻 本地測試指令

### 基本測試指令
```bash
# 執行所有測試
npm test

# 執行單元測試
npm run test:unit

# 執行整合測試
npm run test:integration

# 執行測試並生成覆蓋率報告
npm run test:coverage

# 監視模式（開發時使用）
npm run test:watch
```

### 程式碼品質檢查
```bash
# ESLint 檢查
npm run lint

# 建置測試
npm run build
```

### 特定測試檔案
```bash
# 執行特定測試檔案
npm test -- src/components/__tests__/AutoCarousel.test.tsx

# 執行特定測試模式
npm test -- --testPathPattern=unit
npm test -- --testPathPattern=integration

# 詳細輸出
npm test -- --verbose
```

## 🔧 環境變數設定

### GitHub Secrets 設定
在 GitHub 專案設定中需要配置以下 Secrets：

#### PayUni 測試環境
```
PAYUNI_SANDBOX_MERCHANT_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=your_sandbox_hash_key
PAYUNI_SANDBOX_HASH_IV=your_sandbox_hash_iv
```

#### Google Sheets API
```
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\n...
TEST_GOOGLE_SHEET_ID=your_test_sheet_id
```

#### Meta Pixel (可選)
```
META_PIXEL_ID=your_pixel_id
META_ACCESS_TOKEN=your_access_token
```

### 本地開發環境變數
創建 `.env.local` 檔案：
```env
# PayUni 設定
PAYUNI_ENVIRONMENT=sandbox
PAYUNI_SANDBOX_MERCHANT_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=your_sandbox_hash_key
PAYUNI_SANDBOX_HASH_IV=your_sandbox_hash_iv

# Google Sheets
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
GOOGLE_SHEET_ID=your_sheet_id

# Meta Pixel
META_PIXEL_ID=your_pixel_id
META_ACCESS_TOKEN=your_access_token
```

## 🧪 測試類型說明

### 單元測試 (Unit Tests)
- **位置**：`src/__tests__/unit/`
- **目的**：測試獨立函數和工具
- **範例**：PayUni 加密解密、表單驗證邏輯

### 整合測試 (Integration Tests)
- **位置**：`src/__tests__/integration/`
- **目的**：測試 API 路由和外部服務整合
- **範例**：付款 API、Google Sheets 操作

### 組件測試 (Component Tests)
- **位置**：`src/components/__tests__/`
- **目的**：測試 React 組件行為
- **範例**：AutoCarousel 輪播組件

## 📊 查看測試結果

### GitHub Actions 結果
1. 前往 GitHub 專案頁面
2. 點擊 "Actions" 標籤
3. 選擇對應的工作流程執行
4. 查看各個 job 的執行結果

### 本地測試結果
```bash
# 基本測試結果
npm test

# 詳細覆蓋率報告
npm run test:coverage
# 報告會生成在 coverage/ 目錄
# 開啟 coverage/lcov-report/index.html 查看詳細報告
```

## 🔍 故障排除

### 常見問題

#### 1. 測試環境變數缺失
**錯誤**：`Environment variable not found`
**解決**：檢查 `.env.local` 或 GitHub Secrets 設定

#### 2. Google Sheets API 權限錯誤
**錯誤**：`403 Forbidden`
**解決**：確認服務帳號有 Google Sheets 存取權限

#### 3. PayUni 測試失敗
**錯誤**：`PayUni API error`
**解決**：確認使用 sandbox 環境和正確的測試金鑰

#### 4. MSW 模擬失敗
**錯誤**：`Request not handled`
**解決**：檢查 `src/__tests__/mocks/handlers.ts` 中的 API 模擬設定

### 除錯技巧

#### 1. 查看詳細測試輸出
```bash
npm test -- --verbose --no-coverage
```

#### 2. 執行特定失敗的測試
```bash
npm test -- --testNamePattern="specific test name"
```

#### 3. 檢查測試環境
```bash
# 檢查環境變數
npm run test -- --detectOpenHandles --forceExit
```

## 📈 最佳實踐

### 1. 測試撰寫原則
- 每個 API 路由都應有對應的整合測試
- 複雜的業務邏輯應有單元測試
- 重要的 UI 組件應有組件測試

### 2. CI/CD 優化
- 使用快取加速依賴安裝
- 並行執行不同類型的測試
- 只在必要時執行完整測試套件

### 3. 環境管理
- 測試環境與生產環境分離
- 使用專用的測試資料庫/試算表
- 定期更新測試資料

## 🔄 持續改進

### 監控指標
- 測試覆蓋率目標：70% 以上
- 測試執行時間：< 5 分鐘
- 建置成功率：> 95%

### 定期檢查
- 每月檢查測試覆蓋率報告
- 定期更新測試資料和模擬設定
- 檢查並修正過時的測試案例

---

**最後更新**：2025-07-11
**維護者**：開發團隊
