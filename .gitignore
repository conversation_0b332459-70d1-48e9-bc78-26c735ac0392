# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# 但保留 .env.example 用於 CI/CD
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# CPU 限制測試報告
cpu-test-report.json
benchmark-report.json
cpu-performance-report.json
*-backup-*.json
cpu-testing-setup-report.json

# 舊的時間戳報告檔案（如果存在）
cpu-test-report-*.json
benchmark-report-*.json