/**
 * 速率限制邏輯
 * 使用記憶體快取實作 IP 基礎的速率限制
 * 適用於 Cloudflare Pages 的無狀態環境
 */

export interface RateLimitConfig {
  windowMs: number; // 時間窗口（毫秒）
  maxRequests: number; // 最大請求數
  skipSuccessfulRequests?: boolean; // 是否跳過成功的請求
  skipFailedRequests?: boolean; // 是否跳過失敗的請求
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
}

// 記憶體快取儲存請求記錄
const requestCache = new Map<string, {
  requests: number[];
  lastCleanup: number;
}>();

// 預設配置
export const DEFAULT_RATE_LIMIT_CONFIG: RateLimitConfig = {
  windowMs: 60 * 1000, // 1 分鐘
  maxRequests: 5, // 每分鐘最多 5 次請求
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
};

/**
 * 清理過期的請求記錄
 */
function cleanupExpiredRequests(key: string, windowMs: number): void {
  const record = requestCache.get(key);
  if (!record) return;

  const now = Date.now();
  const cutoff = now - windowMs;

  // 移除過期的請求時間戳
  record.requests = record.requests.filter(timestamp => timestamp > cutoff);
  record.lastCleanup = now;

  // 如果沒有請求記錄，移除整個記錄
  if (record.requests.length === 0) {
    requestCache.delete(key);
  }
}

/**
 * 檢查速率限制
 */
export function checkRateLimit(
  identifier: string,
  config: RateLimitConfig = DEFAULT_RATE_LIMIT_CONFIG
): RateLimitResult {
  // 在測試環境中跳過速率限制
  if (process.env.NODE_ENV === 'test') {
    return {
      allowed: true,
      remaining: config.maxRequests,
      resetTime: Date.now() + config.windowMs,
      totalHits: 0,
    };
  }

  const now = Date.now();
  const windowStart = now - config.windowMs;

  // 清理過期記錄
  cleanupExpiredRequests(identifier, config.windowMs);

  // 取得或建立請求記錄
  let record = requestCache.get(identifier);
  if (!record) {
    record = {
      requests: [],
      lastCleanup: now,
    };
    requestCache.set(identifier, record);
  }

  // 計算當前窗口內的請求數
  const requestsInWindow = record.requests.filter(timestamp => timestamp > windowStart);
  const totalHits = requestsInWindow.length;

  // 檢查是否超過限制
  const allowed = totalHits < config.maxRequests;
  const remaining = Math.max(0, config.maxRequests - totalHits);

  // 計算重置時間（最早請求的過期時間）
  const resetTime = requestsInWindow.length > 0
    ? requestsInWindow[0] + config.windowMs
    : now + config.windowMs;

  return {
    allowed,
    remaining,
    resetTime,
    totalHits,
  };
}

/**
 * 記錄請求
 */
export function recordRequest(
  identifier: string,
  success: boolean = true,
  config: RateLimitConfig = DEFAULT_RATE_LIMIT_CONFIG
): void {
  // 根據配置決定是否記錄此請求
  if (success && config.skipSuccessfulRequests) return;
  if (!success && config.skipFailedRequests) return;

  const now = Date.now();

  // 取得或建立請求記錄
  let record = requestCache.get(identifier);
  if (!record) {
    record = {
      requests: [],
      lastCleanup: now,
    };
    requestCache.set(identifier, record);
  }

  // 添加新的請求時間戳
  record.requests.push(now);

  // 立即清理過期記錄
  cleanupExpiredRequests(identifier, config.windowMs);
}

/**
 * 從 Request 物件取得客戶端 IP
 */
export function getClientIP(request: Request): string {
  // Cloudflare 提供的 IP 標頭
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) return cfConnectingIP;

  // 其他常見的 IP 標頭
  const xForwardedFor = request.headers.get('x-forwarded-for');
  if (xForwardedFor) {
    // X-Forwarded-For 可能包含多個 IP，取第一個
    return xForwardedFor.split(',')[0].trim();
  }

  const xRealIP = request.headers.get('x-real-ip');
  if (xRealIP) return xRealIP;

  // 備用方案
  return 'unknown';
}

/**
 * 建立基於 email 的速率限制
 */
export function checkEmailRateLimit(
  email: string,
  config: RateLimitConfig = {
    windowMs: 5 * 60 * 1000, // 5 分鐘
    maxRequests: 2, // 每 5 分鐘最多 2 次
  }
): RateLimitResult {
  // 防止 email 為 undefined 或 null 時出錯
  if (!email || typeof email !== 'string') {
    return {
      allowed: false,
      remaining: 0,
      resetTime: Date.now() + config.windowMs,
      totalHits: 0,
    };
  }

  const identifier = `email:${email.toLowerCase()}`;
  return checkRateLimit(identifier, config);
}

/**
 * 記錄 email 請求
 */
export function recordEmailRequest(
  email: string,
  success: boolean = true,
  config: RateLimitConfig = {
    windowMs: 5 * 60 * 1000,
    maxRequests: 2,
  }
): void {
  // 防止 email 為 undefined 或 null 時出錯
  if (!email || typeof email !== 'string') {
    console.warn('recordEmailRequest: 無效的 email 參數:', email);
    return;
  }

  const identifier = `email:${email.toLowerCase()}`;
  recordRequest(identifier, success, config);
}
