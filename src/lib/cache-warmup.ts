/**
 * 快取預熱機制
 * 在應用啟動時自動載入常用資料
 */

import { warmupCache } from '@/lib/cache-implementation';

let isWarmedUp = false;
let warmupPromise: Promise<void> | null = null;

/**
 * 執行快取預熱
 * 只會執行一次，後續呼叫會返回相同的 Promise
 */
export async function performCacheWarmup(): Promise<void> {
  // 如果已經預熱過，直接返回
  if (isWarmedUp) {
    return;
  }

  // 如果正在預熱中，返回現有的 Promise
  if (warmupPromise) {
    return warmupPromise;
  }

  // 開始預熱
  warmupPromise = (async () => {
    try {
      console.log('🔥 開始快取預熱...');
      const startTime = performance.now();

      await warmupCache();

      const duration = performance.now() - startTime;
      console.log(`🎉 快取預熱完成，耗時 ${duration.toFixed(2)}ms`);
      
      isWarmedUp = true;
    } catch (error) {
      console.error('❌ 快取預熱失敗:', error);
      // 重置狀態，允許重試
      warmupPromise = null;
      throw error;
    }
  })();

  return warmupPromise;
}

/**
 * 檢查快取是否已預熱
 */
export function isCacheWarmedUp(): boolean {
  return isWarmedUp;
}

/**
 * 重置預熱狀態（用於測試或強制重新預熱）
 */
export function resetWarmupStatus(): void {
  isWarmedUp = false;
  warmupPromise = null;
}

// 在伺服器端自動執行預熱
if (typeof window === 'undefined') {
  // 延遲執行，避免阻塞應用啟動
  setTimeout(() => {
    performCacheWarmup().catch(console.error);
  }, 1000); // 1秒後開始預熱
}
