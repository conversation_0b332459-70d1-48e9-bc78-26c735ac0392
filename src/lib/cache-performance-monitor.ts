/**
 * 快取效能監控工具
 * 提供詳細的效能分析和監控功能
 */

interface PerformanceMetrics {
  cacheHits: number;
  cacheMisses: number;
  totalRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  lastResetTime: Date;
}

interface CacheOperationLog {
  timestamp: Date;
  operation: 'hit' | 'miss' | 'set' | 'clear';
  key: string;
  responseTime?: number;
  dataSize?: number;
}

class CachePerformanceMonitor {
  private metrics!: PerformanceMetrics;
  private operationLogs: CacheOperationLog[] = [];
  private maxLogSize = 1000; // 最多保留 1000 條日誌

  constructor() {
    this.resetMetrics();
  }

  /**
   * 重置效能指標
   */
  resetMetrics(): void {
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      totalRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      lastResetTime: new Date()
    };
  }

  /**
   * 記錄快取命中
   */
  recordCacheHit(key: string, responseTime: number): void {
    this.metrics.cacheHits++;
    this.metrics.totalRequests++;
    this.updateAverageResponseTime(responseTime);
    this.updateCacheHitRate();
    
    this.addOperationLog({
      timestamp: new Date(),
      operation: 'hit',
      key,
      responseTime
    });

    if (this.isLoggingEnabled()) {
      console.log(`🎯 快取命中: ${key} (${responseTime.toFixed(2)}ms) - 命中率: ${this.metrics.cacheHitRate.toFixed(1)}%`);
    }
  }

  /**
   * 記錄快取未命中
   */
  recordCacheMiss(key: string, responseTime: number, dataSize?: number): void {
    this.metrics.cacheMisses++;
    this.metrics.totalRequests++;
    this.updateAverageResponseTime(responseTime);
    this.updateCacheHitRate();
    
    this.addOperationLog({
      timestamp: new Date(),
      operation: 'miss',
      key,
      responseTime,
      dataSize
    });

    if (this.isLoggingEnabled()) {
      console.log(`📡 快取未命中: ${key} (${responseTime.toFixed(2)}ms) - 命中率: ${this.metrics.cacheHitRate.toFixed(1)}%`);
    }
  }

  /**
   * 記錄快取設定操作
   */
  recordCacheSet(key: string, dataSize: number): void {
    this.addOperationLog({
      timestamp: new Date(),
      operation: 'set',
      key,
      dataSize
    });

    if (this.isLoggingEnabled()) {
      console.log(`💾 快取設定: ${key} (${this.formatBytes(dataSize)})`);
    }
  }

  /**
   * 記錄快取清除操作
   */
  recordCacheClear(key?: string): void {
    this.addOperationLog({
      timestamp: new Date(),
      operation: 'clear',
      key: key || 'all'
    });

    if (this.isLoggingEnabled()) {
      console.log(`🧹 快取清除: ${key || '全部'}`);
    }
  }

  /**
   * 獲取當前效能指標
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 獲取詳細的效能報告
   */
  getPerformanceReport(): {
    metrics: PerformanceMetrics;
    recommendations: string[];
    recentLogs: CacheOperationLog[];
  } {
    const recommendations = this.generateRecommendations();
    const recentLogs = this.operationLogs.slice(-20); // 最近 20 條日誌

    return {
      metrics: this.getMetrics(),
      recommendations,
      recentLogs
    };
  }

  /**
   * 生成效能建議
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const { cacheHitRate, totalRequests, averageResponseTime } = this.metrics;

    if (totalRequests < 10) {
      recommendations.push('資料量太少，建議增加測試請求數量以獲得更準確的分析');
    }

    if (cacheHitRate < 50) {
      recommendations.push('快取命中率偏低，建議檢查 TTL 設定或增加預熱範圍');
    } else if (cacheHitRate > 90) {
      recommendations.push('快取命中率優秀，系統效能良好');
    }

    if (averageResponseTime > 100) {
      recommendations.push('平均響應時間較高，建議優化資料獲取邏輯');
    }

    if (this.operationLogs.length > this.maxLogSize * 0.8) {
      recommendations.push('日誌數量接近上限，建議定期清理或增加日誌容量');
    }

    return recommendations;
  }

  /**
   * 更新平均響應時間
   */
  private updateAverageResponseTime(responseTime: number): void {
    const total = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime;
    this.metrics.averageResponseTime = total / this.metrics.totalRequests;
  }

  /**
   * 更新快取命中率
   */
  private updateCacheHitRate(): void {
    this.metrics.cacheHitRate = (this.metrics.cacheHits / this.metrics.totalRequests) * 100;
  }

  /**
   * 新增操作日誌
   */
  private addOperationLog(log: CacheOperationLog): void {
    this.operationLogs.push(log);
    
    // 保持日誌數量在限制內
    if (this.operationLogs.length > this.maxLogSize) {
      this.operationLogs = this.operationLogs.slice(-this.maxLogSize);
    }
  }

  /**
   * 檢查是否啟用日誌記錄
   */
  private isLoggingEnabled(): boolean {
    return process.env.CACHE_LOGGING === 'true' || process.env.NODE_ENV === 'development';
  }

  /**
   * 格式化位元組大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 匯出效能資料為 JSON
   */
  exportData(): string {
    return JSON.stringify({
      metrics: this.metrics,
      logs: this.operationLogs,
      exportTime: new Date()
    }, null, 2);
  }

  /**
   * 清除所有日誌和指標
   */
  clearAll(): void {
    this.resetMetrics();
    this.operationLogs = [];
    
    if (this.isLoggingEnabled()) {
      console.log('🔄 快取效能監控資料已清除');
    }
  }
}

// 建立全域效能監控實例
export const cachePerformanceMonitor = new CachePerformanceMonitor();

// 擴展全域物件類型
declare global {
  var __cachePerformanceMonitor: CachePerformanceMonitor | undefined;
}

// 在開發環境中掛載到全域物件
if (process.env.NODE_ENV === 'development' && typeof globalThis !== 'undefined') {
  globalThis.__cachePerformanceMonitor = cachePerformanceMonitor;
}

export default cachePerformanceMonitor;
