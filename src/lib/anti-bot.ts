/**
 * 防機器人攻擊核心邏輯
 * 提供多層防護：蜜罐欄位、提交時間檢查、重複提交檢查等
 */

export interface AntiBotConfig {
  minSubmissionTime: number; // 最小提交時間（秒）
  maxSubmissionTime: number; // 最大提交時間（秒）
  duplicateSubmissionWindow: number; // 重複提交檢查窗口（分鐘）
  honeypotFieldName: string; // 蜜罐欄位名稱
}

export interface SubmissionData {
  email: string;
  formStartTime: number;
  submissionTime: number;
  honeypotValue?: string;
  userAgent?: string;
  ip?: string;
}

export interface ValidationResult {
  isValid: boolean;
  reason?: string;
  riskScore: number; // 0-100，越高越可疑
}

// 預設配置
export const DEFAULT_ANTI_BOT_CONFIG: AntiBotConfig = {
  minSubmissionTime: 3, // 至少 3 秒
  maxSubmissionTime: 1800, // 最多 30 分鐘
  duplicateSubmissionWindow: 5, // 5 分鐘內不允許重複提交
  honeypotFieldName: 'website', // 蜜罐欄位名稱
};

/**
 * 驗證蜜罐欄位
 */
export function validateHoneypot(honeypotValue: string | undefined): ValidationResult {
  // 檢查是否有任何內容（包括空白字元）
  if (honeypotValue && honeypotValue.length > 0) {
    return {
      isValid: false,
      reason: '蜜罐欄位被填寫，疑似機器人',
      riskScore: 100
    };
  }

  return {
    isValid: true,
    riskScore: 0
  };
}

/**
 * 驗證提交時間
 */
export function validateSubmissionTime(
  formStartTime: number,
  submissionTime: number,
  config: AntiBotConfig = DEFAULT_ANTI_BOT_CONFIG
): ValidationResult {
  const timeDiff = (submissionTime - formStartTime) / 1000; // 轉換為秒
  
  if (timeDiff < config.minSubmissionTime) {
    return {
      isValid: false,
      reason: `提交過快（${timeDiff.toFixed(1)}秒），疑似機器人`,
      riskScore: 90
    };
  }
  
  if (timeDiff > config.maxSubmissionTime) {
    return {
      isValid: false,
      reason: `表單過期（${Math.round(timeDiff / 60)}分鐘），請重新填寫`,
      riskScore: 30
    };
  }
  
  // 計算風險分數：提交時間越短，風險越高
  let riskScore = 0;
  if (timeDiff < 10) {
    riskScore = Math.max(0, 50 - (timeDiff * 5));
  }
  
  return {
    isValid: true,
    riskScore
  };
}

/**
 * 檢查重複提交
 */
export function validateDuplicateSubmission(
  email: string,
  lastSubmissionTime: string | null,
  config: AntiBotConfig = DEFAULT_ANTI_BOT_CONFIG
): ValidationResult {
  if (!lastSubmissionTime) {
    return {
      isValid: true,
      riskScore: 0
    };
  }
  
  const lastTime = new Date(lastSubmissionTime);
  const now = new Date();
  const diffMinutes = (now.getTime() - lastTime.getTime()) / (1000 * 60);
  
  if (diffMinutes < config.duplicateSubmissionWindow) {
    return {
      isValid: false,
      reason: `請勿在 ${config.duplicateSubmissionWindow} 分鐘內重複提交`,
      riskScore: 80
    };
  }
  
  return {
    isValid: true,
    riskScore: 0
  };
}

/**
 * 分析 User Agent 可疑程度
 */
export function analyzeUserAgent(userAgent: string | undefined): ValidationResult {
  if (!userAgent) {
    return {
      isValid: true,
      reason: '缺少 User Agent',
      riskScore: 20
    };
  }
  
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /requests/i,
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(userAgent)) {
      return {
        isValid: false,
        reason: '可疑的 User Agent',
        riskScore: 95
      };
    }
  }
  
  // 檢查是否為常見瀏覽器
  const browserPatterns = [
    /Chrome/i,
    /Firefox/i,
    /Safari/i,
    /Edge/i,
    /Opera/i,
  ];
  
  const hasValidBrowser = browserPatterns.some(pattern => pattern.test(userAgent));
  if (!hasValidBrowser) {
    return {
      isValid: true,
      reason: '非常見瀏覽器',
      riskScore: 40
    };
  }
  
  return {
    isValid: true,
    riskScore: 0
  };
}

/**
 * 綜合驗證提交資料
 */
export function validateSubmission(
  data: SubmissionData,
  lastSubmissionTime: string | null = null,
  config: AntiBotConfig = DEFAULT_ANTI_BOT_CONFIG
): ValidationResult {
  const validations = [
    validateHoneypot(data.honeypotValue),
    validateSubmissionTime(data.formStartTime, data.submissionTime, config),
    validateDuplicateSubmission(data.email, lastSubmissionTime, config),
    analyzeUserAgent(data.userAgent),
  ];
  
  // 檢查是否有任何驗證失敗
  const failedValidation = validations.find(v => !v.isValid);
  if (failedValidation) {
    return failedValidation;
  }
  
  // 計算總風險分數
  const totalRiskScore = validations.reduce((sum, v) => sum + v.riskScore, 0);
  const averageRiskScore = totalRiskScore / validations.length;
  
  return {
    isValid: averageRiskScore < 50, // 平均風險分數低於 50 才通過
    reason: averageRiskScore >= 50 ? '綜合風險評估過高' : undefined,
    riskScore: averageRiskScore
  };
}

/**
 * 生成表單安全 token（用於驗證表單來源）
 */
export function generateFormToken(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  return `${timestamp}_${random}`;
}

/**
 * 驗證表單 token
 */
export function validateFormToken(token: string, maxAge: number = 1800000): boolean {
  try {
    const [timestampStr] = token.split('_');
    const timestamp = parseInt(timestampStr);
    const now = Date.now();
    
    return (now - timestamp) <= maxAge;
  } catch {
    return false;
  }
}
