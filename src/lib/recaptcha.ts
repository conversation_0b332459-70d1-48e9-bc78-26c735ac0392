/**
 * reCAPTCHA 驗證邏輯
 * 提供 reCAPTCHA v3 的伺服器端驗證功能
 */

import { RECAPTCHA_CONFIG } from '@/config/environment-config';

export interface RecaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  error_codes?: string[];
}

export interface RecaptchaValidationResult {
  isValid: boolean;
  score: number;
  reason?: string;
}

/**
 * 驗證 reCAPTCHA token
 * @param token - 前端獲取的 reCAPTCHA token
 * @param expectedAction - 預期的 action 名稱
 * @param minScore - 最小接受分數（0-1，預設 0.5）
 */
export async function verifyRecaptcha(
  token: string,
  expectedAction: string = 'submit',
  minScore: number = 0.5
): Promise<RecaptchaValidationResult> {
  const secretKey = RECAPTCHA_CONFIG.getSecretKey();
  
  if (!secretKey) {
    console.warn('reCAPTCHA Secret Key 未設定，跳過驗證');
    return {
      isValid: true,
      score: 1.0,
      reason: 'reCAPTCHA 未啟用'
    };
  }

  if (!token) {
    return {
      isValid: false,
      score: 0,
      reason: '缺少 reCAPTCHA token'
    };
  }

  try {
    const verifyUrl = RECAPTCHA_CONFIG.getVerifyUrl();
    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: secretKey,
        response: token,
      }),
    });

    if (!response.ok) {
      throw new Error(`reCAPTCHA API 回應錯誤: ${response.status}`);
    }

    const result: RecaptchaVerificationResult = await response.json();
    
    console.log('🔍 reCAPTCHA 驗證結果:', {
      success: result.success,
      score: result.score,
      action: result.action,
      expectedAction,
      error_codes: result.error_codes
    });

    if (!result.success) {
      return {
        isValid: false,
        score: 0,
        reason: `reCAPTCHA 驗證失敗: ${result.error_codes?.join(', ') || '未知錯誤'}`
      };
    }

    // 檢查 action 是否符合預期
    if (result.action && result.action !== expectedAction) {
      return {
        isValid: false,
        score: result.score || 0,
        reason: `reCAPTCHA action 不符合預期: ${result.action} !== ${expectedAction}`
      };
    }

    // 檢查分數是否達到最小要求
    const score = result.score || 0;
    if (score < minScore) {
      return {
        isValid: false,
        score,
        reason: `reCAPTCHA 分數過低: ${score} < ${minScore}`
      };
    }

    return {
      isValid: true,
      score,
    };

  } catch (error) {
    console.error('reCAPTCHA 驗證錯誤:', error);
    return {
      isValid: false,
      score: 0,
      reason: `reCAPTCHA 驗證異常: ${error instanceof Error ? error.message : '未知錯誤'}`
    };
  }
}

/**
 * 根據分數評估風險等級
 */
export function assessRiskLevel(score: number): {
  level: 'low' | 'medium' | 'high' | 'very_high';
  description: string;
} {
  if (score >= 0.9) {
    return {
      level: 'low',
      description: '極低風險，很可能是真實用戶'
    };
  } else if (score >= 0.7) {
    return {
      level: 'low',
      description: '低風險，可能是真實用戶'
    };
  } else if (score >= 0.5) {
    return {
      level: 'medium',
      description: '中等風險，需要額外驗證'
    };
  } else if (score >= 0.3) {
    return {
      level: 'high',
      description: '高風險，可能是機器人'
    };
  } else {
    return {
      level: 'very_high',
      description: '極高風險，很可能是機器人'
    };
  }
}

/**
 * 檢查 reCAPTCHA 是否已啟用
 */
export function isRecaptchaEnabled(): boolean {
  return process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED === 'true' && 
         !!RECAPTCHA_CONFIG.getSiteKey() && 
         !!RECAPTCHA_CONFIG.getSecretKey();
}

/**
 * 取得 reCAPTCHA 配置摘要
 */
export function getRecaptchaConfig() {
  return {
    enabled: isRecaptchaEnabled(),
    siteKey: RECAPTCHA_CONFIG.getSiteKey(),
    hasSecretKey: !!RECAPTCHA_CONFIG.getSecretKey(),
  };
}
