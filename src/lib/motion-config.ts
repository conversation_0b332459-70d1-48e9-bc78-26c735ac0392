/**
 * Motion.dev 動畫配置文件
 * 統一管理 Pangea 網站的動畫效果
 */

// 基礎動畫時長配置
export const ANIMATION_DURATION = {
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8,
} as const;

// 緩動函數配置
export const EASING = {
  easeOut: [0.0, 0.0, 0.2, 1],
  easeIn: [0.4, 0.0, 1, 1],
  easeInOut: [0.4, 0.0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
} as const;

// 品牌色彩配置
export const BRAND_COLORS = {
  primary: '#2b354d',
  gold: '#f59e0b',
  goldGradient: 'linear-gradient(45deg, #f59e0b, #ea580c, #f59e0b)',
  white: '#ffffff',
  shadow: 'rgba(43, 53, 77, 0.3)',
} as const;

// 1. 奢華按鈕動畫變體
export const luxuryButtonVariants = {
  initial: {
    scale: 1,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    background: BRAND_COLORS.goldGradient,
  },
  hover: {
    scale: 1.02,
    boxShadow: `0 10px 25px ${BRAND_COLORS.shadow}`,
    background: BRAND_COLORS.goldGradient,
    transition: {
      duration: ANIMATION_DURATION.normal,
      ease: EASING.easeOut,
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: ANIMATION_DURATION.fast,
      ease: EASING.easeInOut,
    },
  },
};

// CTA 按鈕專用變體（白底藍字）
export const ctaButtonVariants = {
  initial: {
    scale: 1,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    backgroundColor: BRAND_COLORS.white,
    color: BRAND_COLORS.primary,
  },
  hover: {
    scale: 1.02,
    boxShadow: `0 10px 25px ${BRAND_COLORS.shadow}`,
    backgroundColor: BRAND_COLORS.white,
    color: BRAND_COLORS.primary,
    transition: {
      duration: ANIMATION_DURATION.normal,
      ease: EASING.easeOut,
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: ANIMATION_DURATION.fast,
      ease: EASING.easeInOut,
    },
  },
};

// 2. 滾動觸發動畫變體
export const scrollTriggerVariants = {
  hidden: {
    opacity: 0,
    y: 50,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: ANIMATION_DURATION.slow,
      ease: EASING.easeOut,
    },
  },
};

// 標題專用滾動動畫（從左滑入）
export const titleScrollVariants = {
  hidden: {
    opacity: 0,
    x: -50,
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: ANIMATION_DURATION.slow,
      ease: EASING.easeOut,
    },
  },
};

// 內容專用滾動動畫（從下淡入）
export const contentScrollVariants = {
  hidden: {
    opacity: 0,
    y: 30,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: ANIMATION_DURATION.slow,
      ease: EASING.easeOut,
      delay: 0.1, // 比標題晚 100ms
    },
  },
};

// 3. 卡片進入動畫變體
export const cardEntranceVariants = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95,
  },
  visible: (index: number) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: ANIMATION_DURATION.slow,
      ease: EASING.easeOut,
      delay: index * 0.1, // 錯開動畫時間
    },
  }),
};

// 卡片懸停效果
export const cardHoverVariants = {
  initial: {
    y: 0,
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  },
  hover: {
    y: -8,
    boxShadow: '0 20px 25px rgba(0, 0, 0, 0.15)',
    transition: {
      duration: ANIMATION_DURATION.normal,
      ease: EASING.easeOut,
    },
  },
};

// 容器動畫變體（用於包含多個子元素的容器）
export const containerVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1, // 子元素錯開 100ms
      delayChildren: 0.2,   // 延遲 200ms 開始
    },
  },
};

// 淡入動畫變體
export const fadeInVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: ANIMATION_DURATION.normal,
      ease: EASING.easeOut,
    },
  },
};

// 圖片懸停效果
export const imageHoverVariants = {
  initial: {
    scale: 1,
    filter: 'brightness(1)',
  },
  hover: {
    scale: 1.05,
    filter: 'brightness(1.1)',
    transition: {
      duration: ANIMATION_DURATION.normal,
      ease: EASING.easeOut,
    },
  },
};

// 響應式動畫配置
export const getResponsiveAnimation = (isMobile: boolean) => ({
  duration: isMobile ? ANIMATION_DURATION.fast : ANIMATION_DURATION.normal,
  scale: isMobile ? 1.01 : 1.02, // 手機上較小的縮放
});

// 無障礙設計支援
export const getAccessibleAnimation = (prefersReducedMotion: boolean) => {
  if (prefersReducedMotion) {
    return {
      transition: { duration: 0 },
      animate: { opacity: 1 },
    };
  }
  return {};
};

// 性能優化配置
export const PERFORMANCE_CONFIG = {
  // 使用 will-change 優化
  willChange: 'transform, opacity',
  // 強制硬體加速
  transform: 'translateZ(0)',
  // 避免重排重繪的屬性
  safeProperties: ['transform', 'opacity', 'filter'],
} as const;
