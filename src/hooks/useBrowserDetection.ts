'use client';

import { useState, useEffect } from 'react';
import { detectBrowser, type BrowserInfo } from '@/utils/browserDetection';

/**
 * 統一的瀏覽器檢測 Hook
 * 提供一致的瀏覽器檢測邏輯，避免重複代碼
 */
export function useBrowserDetection() {
  const [browserInfo, setBrowserInfo] = useState<BrowserInfo>({
    isIOS: false,
    isSafari: false,
    isIOSSafari: false,
    isChrome: false,
    isFirefox: false,
    isEdge: false,
    isMobile: false,
    supportsModernFeatures: true,
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 延遲檢測以確保在客戶端執行
    const timer = setTimeout(() => {
      const info = detectBrowser();
      setBrowserInfo(info);
      setIsLoading(false);
      
      // 添加瀏覽器類名到 body
      if (info.isIOSSafari) {
        document.body.classList.add('ios-safari', 'ios-safari-detected');
      }
      if (info.isIOS) {
        document.body.classList.add('ios');
      }
      if (info.isMobile) {
        document.body.classList.add('mobile');
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return {
    ...browserInfo,
    isLoading,
    // 便捷方法
    shouldUseSimpleAnimation: browserInfo.isIOSSafari,
    shouldUseCompatibleStyles: browserInfo.isIOSSafari,
  };
}

/**
 * 簡化的 iOS Safari 檢測 Hook
 * 適用於只需要知道是否為 iOS Safari 的場景
 */
export function useIsIOSSafari() {
  const { isIOSSafari, isLoading } = useBrowserDetection();
  return { isIOSSafari, isLoading };
}

/**
 * 動畫兼容性 Hook
 * 根據瀏覽器類型決定是否使用簡化動畫
 */
export function useAnimationCompatibility() {
  const { isIOSSafari, isLoading } = useBrowserDetection();
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        setPrefersReducedMotion(mediaQuery.matches);
        
        const handleChange = (e: MediaQueryListEvent) => {
          setPrefersReducedMotion(e.matches);
        };
        
        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
      }
    } catch (error) {
      console.warn('prefers-reduced-motion detection failed:', error);
      // 在 iOS Safari 上默認啟用簡化動畫
      setPrefersReducedMotion(isIOSSafari);
    }
  }, [isIOSSafari]);

  return {
    shouldUseSimpleAnimation: prefersReducedMotion || isIOSSafari,
    isLoading,
    prefersReducedMotion,
    isIOSSafari,
  };
}
