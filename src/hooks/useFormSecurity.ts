/**
 * 表單安全 Hook
 * 提供防機器人攻擊的前端邏輯
 */

import { useState, useEffect, useCallback } from 'react';
import { generateFormToken } from '@/lib/anti-bot';

export interface FormSecurityState {
  honeypotValue: string;
  formStartTime: number;
  securityToken: string;
  isReady: boolean;
}

export interface FormSecurityActions {
  setHoneypotValue: (value: string) => void;
  validateBeforeSubmit: () => {
    isValid: boolean;
    reason?: string;
  };
  resetSecurity: () => void;
}

/**
 * 表單安全 Hook
 * 管理蜜罐欄位、提交時間追蹤等安全功能
 */
export function useFormSecurity(): FormSecurityState & FormSecurityActions {
  const [honeypotValue, setHoneypotValue] = useState('');
  const [formStartTime, setFormStartTime] = useState(0);
  const [securityToken, setSecurityToken] = useState('');
  const [isReady, setIsReady] = useState(false);

  // 初始化表單安全狀態
  useEffect(() => {
    const startTime = Date.now();
    const token = generateFormToken();
    
    setFormStartTime(startTime);
    setSecurityToken(token);
    setIsReady(true);
  }, []);

  // 驗證提交前的安全檢查
  const validateBeforeSubmit = useCallback(() => {
    // 檢查蜜罐欄位
    if (honeypotValue.trim() !== '') {
      return {
        isValid: false,
        reason: '表單驗證失敗，請重新整理頁面後再試'
      };
    }

    // 檢查提交時間
    const now = Date.now();
    const timeDiff = (now - formStartTime) / 1000;
    
    if (timeDiff < 3) {
      return {
        isValid: false,
        reason: '請稍等片刻再提交表單'
      };
    }

    if (timeDiff > 1800) { // 30 分鐘
      return {
        isValid: false,
        reason: '表單已過期，請重新整理頁面後再試'
      };
    }

    return {
      isValid: true
    };
  }, [honeypotValue, formStartTime]);

  // 重置安全狀態
  const resetSecurity = useCallback(() => {
    setHoneypotValue('');
    const startTime = Date.now();
    const token = generateFormToken();
    
    setFormStartTime(startTime);
    setSecurityToken(token);
  }, []);

  return {
    honeypotValue,
    formStartTime,
    securityToken,
    isReady,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  };
}

/**
 * reCAPTCHA Hook
 * 管理 reCAPTCHA v3 整合
 */
export interface RecaptchaState {
  isLoaded: boolean;
  isReady: boolean;
  error: string | null;
}

export interface RecaptchaActions {
  executeRecaptcha: (action: string) => Promise<string | null>;
  resetRecaptcha: () => void;
}

export function useRecaptcha(siteKey?: string): RecaptchaState & RecaptchaActions {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 載入 reCAPTCHA 腳本
  useEffect(() => {
    if (!siteKey) {
      setError('reCAPTCHA site key 未設定');
      return;
    }

    // 檢查是否已載入
    if (window.grecaptcha) {
      setIsLoaded(true);
      setIsReady(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setIsLoaded(true);
      
      // 等待 reCAPTCHA 準備就緒
      window.grecaptcha.ready(() => {
        setIsReady(true);
      });
    };

    script.onerror = () => {
      setError('reCAPTCHA 載入失敗');
    };

    document.head.appendChild(script);

    return () => {
      // 清理腳本（如果需要）
      const existingScript = document.querySelector(`script[src*="recaptcha"]`);
      if (existingScript && existingScript.parentNode) {
        existingScript.parentNode.removeChild(existingScript);
      }
    };
  }, [siteKey]);

  // 執行 reCAPTCHA 驗證
  const executeRecaptcha = useCallback(async (action: string): Promise<string | null> => {
    if (!isReady || !window.grecaptcha || !siteKey) {
      console.warn('reCAPTCHA 尚未準備就緒');
      return null;
    }

    try {
      const token = await window.grecaptcha.execute(siteKey, { action });
      return token;
    } catch (error) {
      console.error('reCAPTCHA 執行失敗:', error);
      setError('reCAPTCHA 驗證失敗');
      return null;
    }
  }, [isReady, siteKey]);

  // 重置 reCAPTCHA 狀態
  const resetRecaptcha = useCallback(() => {
    if (window.grecaptcha) {
      window.grecaptcha.reset();
    }
    setError(null);
  }, []);

  return {
    isLoaded,
    isReady,
    error,
    executeRecaptcha,
    resetRecaptcha,
  };
}

// 擴展 Window 介面以支援 reCAPTCHA
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
      reset: () => void;
    };
  }
}
