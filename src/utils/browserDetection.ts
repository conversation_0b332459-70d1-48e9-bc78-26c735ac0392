/**
 * 瀏覽器檢測工具
 * 用於識別不同瀏覽器和設備，特別針對 iOS Safari 兼容性問題
 */

export interface BrowserInfo {
  isIOS: boolean;
  isSafari: boolean;
  isIOSSafari: boolean;
  isChrome: boolean;
  isFirefox: boolean;
  isEdge: boolean;
  isMobile: boolean;
  version?: string;
  supportsModernFeatures: boolean;
}

/**
 * 檢測當前瀏覽器信息
 */
export function detectBrowser(): BrowserInfo {
  if (typeof window === 'undefined') {
    // 服務器端渲染時的默認值
    return {
      isIOS: false,
      isSafari: false,
      isIOSSafari: false,
      isChrome: false,
      isFirefox: false,
      isEdge: false,
      isMobile: false,
      supportsModernFeatures: true,
    };
  }

  const userAgent = window.navigator.userAgent;
  
  // 檢測 iOS（包括新的 iPad 檢測和更準確的檢測）
  const isIOS = /iPad|iPhone|iPod/.test(userAgent) ||
    (userAgent.includes('Macintosh') && 'ontouchend' in document) ||
    (userAgent.includes('Macintosh') && navigator.maxTouchPoints > 1);

  // 檢測 Safari（更精確的檢測）
  const isSafari = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS|OPiOS/.test(userAgent);

  // 檢測 iOS Safari（更嚴格的檢測）
  const isIOSSafari = isIOS && isSafari && !userAgent.includes('GSA');
  
  // 檢測其他瀏覽器
  const isChrome = /Chrome/.test(userAgent) && !/Edge/.test(userAgent);
  const isFirefox = /Firefox/.test(userAgent);
  const isEdge = /Edge/.test(userAgent);
  
  // 檢測移動設備
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  // 提取版本號（簡化版本）
  let version: string | undefined;
  if (isIOSSafari) {
    const match = userAgent.match(/OS (\d+)_(\d+)/);
    if (match) {
      version = `${match[1]}.${match[2]}`;
    }
  }
  
  // 檢測是否支持現代功能
  const supportsModernFeatures = checkModernFeatureSupport();
  
  return {
    isIOS,
    isSafari,
    isIOSSafari,
    isChrome,
    isFirefox,
    isEdge,
    isMobile,
    version,
    supportsModernFeatures,
  };
}

/**
 * 檢測是否支持現代瀏覽器功能
 */
function checkModernFeatureSupport(): boolean {
  if (typeof window === 'undefined') return true;
  
  try {
    // 檢測關鍵的現代功能
    const hasIntersectionObserver = 'IntersectionObserver' in window;
    const hasMatchMedia = 'matchMedia' in window;
    const hasRequestAnimationFrame = 'requestAnimationFrame' in window;
    const hasPromise = 'Promise' in window;
    const hasAsyncAwait = (function() {
      try {
        return (async () => {})().constructor === Promise;
      } catch {
        return false;
      }
    })();
    
    return hasIntersectionObserver && hasMatchMedia && hasRequestAnimationFrame && hasPromise && hasAsyncAwait;
  } catch {
    return false;
  }
}

/**
 * 檢測是否為舊版 iOS Safari（可能有兼容性問題）
 */
export function isLegacyIOSSafari(): boolean {
  const browser = detectBrowser();
  
  if (!browser.isIOSSafari || !browser.version) {
    return false;
  }
  
  // iOS 14 以下版本可能有兼容性問題
  const majorVersion = parseInt(browser.version.split('.')[0]);
  return majorVersion < 14;
}



/**
 * 添加瀏覽器特定的 CSS 類名到 body
 */
export function addBrowserClasses(): void {
  if (typeof window === 'undefined') return;
  
  const browser = detectBrowser();
  const body = document.body;
  
  // 移除現有的瀏覽器類名
  body.classList.remove('ios', 'safari', 'ios-safari', 'chrome', 'firefox', 'edge', 'mobile', 'legacy');
  
  // 添加新的類名
  if (browser.isIOS) body.classList.add('ios');
  if (browser.isSafari) body.classList.add('safari');
  if (browser.isIOSSafari) body.classList.add('ios-safari');
  if (browser.isChrome) body.classList.add('chrome');
  if (browser.isFirefox) body.classList.add('firefox');
  if (browser.isEdge) body.classList.add('edge');
  if (browser.isMobile) body.classList.add('mobile');
  if (isLegacyIOSSafari()) body.classList.add('legacy');
}


