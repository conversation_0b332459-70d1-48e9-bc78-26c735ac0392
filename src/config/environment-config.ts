/**
 * 統一的環境配置管理
 * 根據 APP_ENVIRONMENT 變數自動選擇對應環境的配置
 */

export type Environment = 'sandbox' | 'production';

/**
 * 取得當前環境
 */
export function getCurrentEnvironment(): Environment {
  // 優先使用新的 APP_ENVIRONMENT，向後相容 PAYUNI_ENVIRONMENT
  const env = process.env.APP_ENVIRONMENT || process.env.PAYUNI_ENVIRONMENT || 'sandbox';
  return env as Environment;
}

/**
 * Google Sheets 配置
 */
export const GOOGLE_SHEETS_CONFIG = {
  getSheetId(): string {
    const env = getCurrentEnvironment();
    const sheetId = env === 'production' 
      ? process.env.GOOGLE_PRODUCTION_SHEET_ID 
      : process.env.GOOGLE_SANDBOX_SHEET_ID;
    
    // 向後相容性
    return sheetId || process.env.GOOGLE_SHEET_ID || '';
  },

  getWatchSheetId(): string {
    const env = getCurrentEnvironment();
    const sheetId = env === 'production' 
      ? process.env.GOOGLE_PRODUCTION_WATCH_SHEET_ID 
      : process.env.GOOGLE_SANDBOX_WATCH_SHEET_ID;
    
    // 向後相容性
    return sheetId || process.env.GOOGLE_WATCH_SHEET_ID || '';
  },

  getBlogSheetId(): string {
    const env = getCurrentEnvironment();
    const sheetId = env === 'production' 
      ? process.env.GOOGLE_PRODUCTION_BLOG_SHEET_ID 
      : process.env.GOOGLE_SANDBOX_BLOG_SHEET_ID;
    
    // 向後相容性
    return sheetId || process.env.GOOGLE_BLOG_SHEET_ID || '';
  },

  getServiceAccountEmail(): string {
    return process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || '';
  },

  getPrivateKey(): string {
    return process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n') || '';
  }
};

/**
 * Meta Pixel 配置
 */
export const META_PIXEL_CONFIG = {
  getPixelId(): string {
    const env = getCurrentEnvironment();
    const pixelId = env === 'production' 
      ? process.env.META_PRODUCTION_PIXEL_ID 
      : process.env.META_SANDBOX_PIXEL_ID;
    
    // 向後相容性
    return pixelId || process.env.META_PIXEL_ID || '';
  },

  getAccessToken(): string {
    const env = getCurrentEnvironment();
    const token = env === 'production' 
      ? process.env.META_PRODUCTION_ACCESS_TOKEN 
      : process.env.META_SANDBOX_ACCESS_TOKEN;
    
    // 向後相容性
    return token || process.env.META_ACCESS_TOKEN || '';
  },

  getTestEventCode(): string {
    const env = getCurrentEnvironment();
    const code = env === 'production' 
      ? process.env.META_PRODUCTION_TEST_EVENT_CODE 
      : process.env.META_SANDBOX_TEST_EVENT_CODE;
    
    // 向後相容性
    return code || process.env.META_TEST_EVENT_CODE || '';
  }
};

/**
 * GTM 配置
 */
export const GTM_CONFIG = {
  getGTMId(): string {
    const env = getCurrentEnvironment();
    const gtmId = env === 'production'
      ? process.env.NEXT_PUBLIC_GTM_PRODUCTION_ID
      : process.env.NEXT_PUBLIC_GTM_SANDBOX_ID;

    // 向後相容性
    return gtmId || process.env.NEXT_PUBLIC_GTM_ID || '';
  },

  getProxyDomain(): string {
    const env = getCurrentEnvironment();
    const domain = env === 'production'
      ? process.env.NEXT_PUBLIC_GTM_PRODUCTION_PROXY_DOMAIN
      : process.env.NEXT_PUBLIC_GTM_SANDBOX_PROXY_DOMAIN;

    // 向後相容性
    return domain || process.env.NEXT_PUBLIC_GTM_PROXY_DOMAIN || '';
  }
};

/**
 * reCAPTCHA 配置
 */
export const RECAPTCHA_CONFIG = {
  getSiteKey(): string {
    const env = getCurrentEnvironment();
    const siteKey = env === 'production'
      ? process.env.NEXT_PUBLIC_RECAPTCHA_PRODUCTION_SITE_KEY
      : process.env.NEXT_PUBLIC_RECAPTCHA_SANDBOX_SITE_KEY;

    // 向後相容性
    return siteKey || process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '';
  },

  getSecretKey(): string {
    const env = getCurrentEnvironment();
    const secretKey = env === 'production'
      ? process.env.RECAPTCHA_PRODUCTION_SECRET_KEY
      : process.env.RECAPTCHA_SANDBOX_SECRET_KEY;

    // 向後相容性
    return secretKey || process.env.RECAPTCHA_SECRET_KEY || '';
  },

  getVerifyUrl(): string {
    return 'https://www.google.com/recaptcha/api/siteverify';
  }
};

/**
 * PayUni 配置（保持現有邏輯，但使用新的環境變數）
 */
export const PAYUNI_CONFIG = {
  ENVIRONMENT: getCurrentEnvironment(),
  
  getMerchantId(): string {
    const env = getCurrentEnvironment();
    return env === 'production' 
      ? process.env.PAYUNI_PRODUCTION_MER_ID || ''
      : process.env.PAYUNI_SANDBOX_MER_ID || '';
  },

  getHashKey(): string {
    const env = getCurrentEnvironment();
    return env === 'production' 
      ? process.env.PAYUNI_PRODUCTION_HASH_KEY || ''
      : process.env.PAYUNI_SANDBOX_HASH_KEY || '';
  },

  getHashIV(): string {
    const env = getCurrentEnvironment();
    return env === 'production' 
      ? process.env.PAYUNI_PRODUCTION_HASH_IV || ''
      : process.env.PAYUNI_SANDBOX_HASH_IV || '';
  },

  getApiUrl(): string {
    const env = getCurrentEnvironment();
    return env === 'production' 
      ? 'https://api.payuni.com.tw/api/upp'
      : 'https://sandbox-api.payuni.com.tw/api/upp';
  },

  getQueryUrl(): string {
    const env = getCurrentEnvironment();
    return env === 'production' 
      ? 'https://api.payuni.com.tw/api/trade_query'
      : 'https://sandbox-api.payuni.com.tw/api/trade_query';
  }
};

/**
 * 環境配置驗證
 */
export function validateEnvironmentConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const env = getCurrentEnvironment();

  // 驗證 PayUni 配置
  if (!PAYUNI_CONFIG.getMerchantId()) {
    errors.push(`PayUni Merchant ID 未設定 (環境: ${env})`);
  }
  if (!PAYUNI_CONFIG.getHashKey()) {
    errors.push(`PayUni Hash Key 未設定 (環境: ${env})`);
  }
  if (!PAYUNI_CONFIG.getHashIV()) {
    errors.push(`PayUni Hash IV 未設定 (環境: ${env})`);
  }

  // 驗證 Google Sheets 配置
  if (!GOOGLE_SHEETS_CONFIG.getServiceAccountEmail()) {
    errors.push('Google Service Account Email 未設定');
  }
  if (!GOOGLE_SHEETS_CONFIG.getPrivateKey()) {
    errors.push('Google Private Key 未設定');
  }
  if (!GOOGLE_SHEETS_CONFIG.getSheetId()) {
    errors.push(`Google Sheet ID 未設定 (環境: ${env})`);
  }

  // 驗證 Meta Pixel 配置（如果啟用 CAPI）
  const capiEnabled = process.env.NEXT_PUBLIC_CAPI_ENABLED === 'true';
  if (capiEnabled) {
    if (!META_PIXEL_CONFIG.getPixelId()) {
      errors.push(`Meta Pixel ID 未設定 (環境: ${env})`);
    }
    if (!META_PIXEL_CONFIG.getAccessToken()) {
      errors.push(`Meta Access Token 未設定 (環境: ${env})`);
    }
  }

  // 驗證 GTM 配置
  if (!GTM_CONFIG.getGTMId()) {
    errors.push(`GTM ID 未設定 (環境: ${env})`);
  }

  // 驗證 reCAPTCHA 配置（如果啟用）
  const recaptchaEnabled = process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED === 'true';
  if (recaptchaEnabled) {
    if (!RECAPTCHA_CONFIG.getSiteKey()) {
      errors.push(`reCAPTCHA Site Key 未設定 (環境: ${env})`);
    }
    if (!RECAPTCHA_CONFIG.getSecretKey()) {
      errors.push(`reCAPTCHA Secret Key 未設定 (環境: ${env})`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 輸出當前環境配置摘要（用於除錯）
 */
export function getEnvironmentSummary() {
  const env = getCurrentEnvironment();
  return {
    environment: env,
    payuni: {
      merchantId: PAYUNI_CONFIG.getMerchantId() ? '已設定' : '未設定',
      apiUrl: PAYUNI_CONFIG.getApiUrl()
    },
    googleSheets: {
      sheetId: GOOGLE_SHEETS_CONFIG.getSheetId() ? '已設定' : '未設定',
      watchSheetId: GOOGLE_SHEETS_CONFIG.getWatchSheetId() ? '已設定' : '未設定',
      blogSheetId: GOOGLE_SHEETS_CONFIG.getBlogSheetId() ? '已設定' : '未設定'
    },
    metaPixel: {
      pixelId: META_PIXEL_CONFIG.getPixelId() ? '已設定' : '未設定',
      capiEnabled: process.env.NEXT_PUBLIC_CAPI_ENABLED === 'true'
    },
    gtm: {
      gtmId: GTM_CONFIG.getGTMId() ? '已設定' : '未設定',
      proxyDomain: GTM_CONFIG.getProxyDomain() ? '已設定' : '未設定'
    },
    recaptcha: {
      siteKey: RECAPTCHA_CONFIG.getSiteKey() ? '已設定' : '未設定',
      secretKey: RECAPTCHA_CONFIG.getSecretKey() ? '已設定' : '未設定',
      enabled: process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED === 'true'
    }
  };
}
