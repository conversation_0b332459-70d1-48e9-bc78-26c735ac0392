"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import { AnimatedButton } from "@/components/ui/animated-button";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { detectBrowser } from "@/utils/browserDetection";

interface WatchCareHeroSectionProps {
  title: string;
  subtitle: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
}

const WatchCareHeroSection = ({
  title,
  subtitle,
  ctaText,
  ctaLink,
  backgroundImage
}: WatchCareHeroSectionProps) => {
  const [isIOSSafari, setIsIOSSafari] = useState(false);

  // iOS Safari 檢測
  useEffect(() => {
    const browser = detectBrowser();
    setIsIOSSafari(browser.isIOSSafari);
    if (browser.isIOSSafari) {
      console.log('WatchCareHeroSection: iOS Safari detected, applying fixes');
    }
  }, []);

  // iOS Safari 特定的標題樣式
  const iosSafariTitleStyle = isIOSSafari ? {
    color: '#ffffff',
    transform: 'none',
    WebkitTransform: 'none',
    position: 'static' as const,
    width: '100%',
    maxWidth: '100%',
    boxSizing: 'border-box' as const,
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
    textAlign: 'center' as const,
    margin: '0 auto',
    display: 'block',
  } : {
    color: '#ffffff'
  };

  return (
    <section className="relative h-[50vh] md:h-[60vh] min-[1200px]:h-[60vh] bg-black overflow-hidden">
      {/* 桌面版：右側背景圖片 */}
      <div className="hidden min-[1200px]:block absolute right-0 top-0 w-2/5 h-full">
        <Image
          src={backgroundImage}
          alt="手錶保養背景"
          fill
          className="object-cover object-left"
          priority
        />
      </div>

      {/* 手機、平板和 iPad Pro 版：全覆蓋背景圖片 */}
      <div className="min-[1200px]:hidden absolute inset-0">
        <Image
          src={backgroundImage}
          alt="手錶保養背景"
          fill
          className="object-cover"
          priority
        />
        {/* 深色遮罩 */}
        <div className="absolute inset-0 bg-black/60" />
      </div>

      {/* 手機、平板和 iPad Pro 版：內容區域 */}
      <div className="min-[1200px]:hidden relative z-10 h-full flex items-center justify-center">
        <div className="text-center max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 主標題 */}
          {isIOSSafari ? (
            <div className="mb-6">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold" style={iosSafariTitleStyle}>
                {title}
              </h2>
            </div>
          ) : (
            <ScrollTriggerMotion
              variants={titleScrollVariants}
              className="mb-6"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold" style={iosSafariTitleStyle}>
                {title}
              </h2>
            </ScrollTriggerMotion>
          )}

          {/* 副標題 */}
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            className="mb-8"
          >
            <p className="text-lg md:text-xl leading-relaxed" style={{ color: '#ffffff', opacity: 0.9 }}>
              {subtitle}
            </p>
          </ScrollTriggerMotion>

          {/* CTA 按鈕 - 置中對齊 */}
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            delay={0.2}
            className="flex justify-center"
          >
            <AnimatedButton
              asChild
              size="xl"
              variant="cta"
              className="py-4 px-6 rounded-lg text-base"
            >
              <a href={ctaLink}>
                {ctaText}
              </a>
            </AnimatedButton>
          </ScrollTriggerMotion>
        </div>
      </div>

      {/* 桌面版：內容區域 - 垂直置中 */}
      <div className="hidden min-[1200px]:flex relative z-10 h-full items-center justify-center">
        <div className="text-center max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 桌面版主標題 */}
          {isIOSSafari ? (
            <div className="mb-6">
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold" style={iosSafariTitleStyle}>
                {title}
              </h2>
            </div>
          ) : (
            <ScrollTriggerMotion
              variants={titleScrollVariants}
              className="mb-6"
            >
              <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold" style={iosSafariTitleStyle}>
                {title}
              </h2>
            </ScrollTriggerMotion>
          )}

          {/* 桌面版副標題 */}
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            className="mb-8"
          >
            <p className="text-lg md:text-xl lg:text-2xl leading-relaxed" style={{ color: '#ffffff', opacity: 0.9 }}>
              {subtitle}
            </p>
          </ScrollTriggerMotion>

          {/* 桌面版 CTA 按鈕 - 置中對齊 */}
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            delay={0.2}
            className="flex justify-center"
          >
            <AnimatedButton
              asChild
              size="xl"
              variant="cta"
              className="py-4 px-6 rounded-lg text-base"
            >
              <a href={ctaLink}>
                {ctaText}
              </a>
            </AnimatedButton>
          </ScrollTriggerMotion>
        </div>
      </div>
    </section>
  );
};

export default WatchCareHeroSection;
