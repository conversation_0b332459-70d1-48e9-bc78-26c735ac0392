"use client";

import { AnimatedButton } from "@/components/ui/animated-button";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { useEffect, useState } from "react";
import { detectBrowser } from "@/utils/browserDetection";


interface ImageHeroSectionProps {
  title: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
}

const ImageHeroSection = ({
  title,
  description,
  ctaText,
  ctaLink,
  backgroundImage
}: ImageHeroSectionProps) => {
  const [isIOSSafari, setIsIOSSafari] = useState(false);

  useEffect(() => {
    const browser = detectBrowser();
    setIsIOSSafari(browser.isIOSSafari);
    if (browser.isIOSSafari) {
      console.log('ImageHeroSection: iOS Safari detected, applying fixes');
    }
  }, []);

  // iOS Safari 特定的標題樣式
  const iosSafariTitleStyle = isIOSSafari ? {
    backgroundImage: 'linear-gradient(to right, #ffffff, #f1f5f9)',
    transform: 'none',
    WebkitTransform: 'none',
    position: 'static' as const,
    width: '100%',
    maxWidth: '100%',
    boxSizing: 'border-box' as const,
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } : {
    backgroundImage: 'linear-gradient(to right, #ffffff, #f1f5f9)'
  };

  return (
    <section className="relative text-white">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      >
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-10 py-12 md:py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 md:px-8 lg:px-16">
          <div className="text-center md:text-left md:max-w-2xl">
            {/* Main Title */}
            {isIOSSafari ? (
              <div className="mb-6">
                <h1
                  className="text-3xl md:text-5xl font-bold tracking-tight bg-gradient-to-r bg-clip-text text-transparent"
                  style={iosSafariTitleStyle}
                >
                  {title}
                </h1>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
                className="mb-6"
              >
                <h1 className="text-3xl md:text-5xl font-bold tracking-tight bg-gradient-to-r bg-clip-text text-transparent" style={iosSafariTitleStyle}>
                  {title}
                </h1>
              </ScrollTriggerMotion>
            )}

            {/* Description */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="text-base md:text-lg opacity-90 mb-8 leading-relaxed space-y-3"
            >
              <div>
                {description.split('\n').map((line, index) => (
                  <p key={index} style={{ color: '#ffffff', opacity: 0.9 }}>
                    {line}
                  </p>
                ))}
              </div>
            </ScrollTriggerMotion>

            {/* CTA Button */}
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.2}
              className="flex justify-center md:justify-start"
            >
              <AnimatedButton
                asChild
                size="xl"
                variant="cta"
                className="py-4 px-6 rounded-lg text-base"
              >
                <a href={ctaLink}>
                  {ctaText}
                </a>
              </AnimatedButton>
            </ScrollTriggerMotion>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImageHeroSection;
