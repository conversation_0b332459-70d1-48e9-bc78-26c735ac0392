"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";

interface TestimonialImage {
  id: number;
  image: string;
  alt: string;
}

interface ExpertTestimonialSectionProps {
  title: string;
  images: TestimonialImage[];
  autoScroll?: boolean;
  speed?: number; // pixels per second
}

const ExpertTestimonialSection = ({
  title,
  images,
  autoScroll = true,
  speed = 30
}: ExpertTestimonialSectionProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const { isIOSSafari } = useIsIOSSafari();
  const scrollRef = useRef<HTMLDivElement>(null);
  const currentPositionRef = useRef(0); // 保存當前滾動位置

  // 燈箱功能
  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);



  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;

      if (e.key === 'Escape') closeLightbox();
      if (e.key === 'ArrowRight') nextImage();
      if (e.key === 'ArrowLeft') prevImage();
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [lightboxOpen, images.length, nextImage, prevImage]);

  // 自動滾動效果 - 跑馬燈
  useEffect(() => {
    if (!autoScroll || isHovered || lightboxOpen) return;

    const scrollContainer = scrollRef.current;
    if (!scrollContainer || images.length === 0) return;

    let animationId: number;

    // 等待一下讓 DOM 完全渲染
    const startAnimation = () => {
      // 如果是第一次啟動，從當前滾動位置開始
      if (currentPositionRef.current === 0 && scrollContainer.scrollLeft > 0) {
        currentPositionRef.current = scrollContainer.scrollLeft;
      }

      const animate = () => {
        currentPositionRef.current += speed / 60; // 60fps

        // 當滾動到一組圖片的寬度時重置位置
        const singleSetWidth = scrollContainer.scrollWidth / 3;
        if (currentPositionRef.current >= singleSetWidth) {
          currentPositionRef.current = 0;
        }

        scrollContainer.scrollLeft = currentPositionRef.current;
        animationId = requestAnimationFrame(animate);
      };

      animationId = requestAnimationFrame(animate);
    };

    // 延遲啟動動畫，確保 DOM 已渲染
    const timer = setTimeout(startAnimation, 100);

    return () => {
      clearTimeout(timer);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      // 保存當前位置，以便下次恢復
      if (scrollContainer) {
        currentPositionRef.current = scrollContainer.scrollLeft;
      }
    };
  }, [speed, images.length, autoScroll, isHovered, lightboxOpen]);

  if (images.length === 0) return null;

  // Duplicate images multiple times for seamless loop
  const displayImages = [...images, ...images, ...images];

  return (
    <>
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 標題 */}
          <div className="text-center mb-12">
            {isIOSSafari ? (
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-8">
                {title}
              </h2>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
              >
                <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-8">
                  {title}
                </h2>
              </ScrollTriggerMotion>
            )}
          </div>

          {/* 圖片輪播 */}
          <div className="relative">
            <div
              ref={scrollRef}
              className="flex gap-6 overflow-x-hidden"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
                {displayImages.map((image, index) => (
                  <div
                    key={`${image.id}-${index}`}
                    className="flex-shrink-0 group cursor-pointer"
                    onClick={() => openLightbox(index % images.length)}
                  >
                    <div className={cn(
                      "relative w-80 sm:w-96 lg:w-[400px] xl:w-[420px] h-48 sm:h-56 lg:h-64 xl:h-72 rounded-xl overflow-hidden",
                      "border border-slate-200 shadow-lg hover:shadow-2xl",
                      "hover:shadow-[#2b354d]/20 transition-all duration-300",
                      "hover:border-[#2b354d]/30 transform hover:-translate-y-1"
                    )}>
                      <Image
                        src={image.image}
                        alt={image.alt}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                        sizes="(max-width: 640px) 320px, (max-width: 1024px) 384px, 400px"
                      />
                      {/* 懸停遮罩 */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                      
                      {/* 點擊提示 */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg">
                          <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </section>

      {/* 燈箱 */}
      {lightboxOpen && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center" style={{ zIndex: 99999 }}>
          {/* 關閉按鈕 - Safari 兼容性修復 */}
          <button
            onClick={closeLightbox}
            className="text-white hover:text-gray-300 transition-colors"
            style={{
              position: 'fixed',
              top: '16px',
              right: '16px',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            <X size={24} />
          </button>

          {/* 上一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={prevImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              left: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronLeft size={16} />
          </button>

          {/* 下一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={nextImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              right: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronRight size={16} />
          </button>

          {/* 燈箱圖片 */}
          <div className="relative max-w-[90vw] max-h-[90vh]">
            <Image
              src={images[currentImageIndex].image}
              alt={images[currentImageIndex].alt}
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
            />
          </div>

          {/* 圖片指示器 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                )}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default ExpertTestimonialSection;
