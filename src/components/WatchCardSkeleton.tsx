const WatchCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-lg border border-slate-200 h-full w-full flex flex-col">
      {/* 圖片骨架 */}
      <div className="relative w-full h-64 bg-slate-200 animate-pulse flex-shrink-0">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 animate-shimmer"></div>
      </div>

      {/* 內容骨架 */}
      <div className="p-6 flex flex-col flex-grow min-w-0">
        {/* 品牌骨架 */}
        <div className="h-4 bg-slate-200 rounded w-20 mb-2 animate-pulse"></div>

        {/* 產品名稱骨架 */}
        <div className="space-y-2 mb-3 flex-grow min-w-0">
          <div className="h-5 bg-slate-200 rounded w-full animate-pulse"></div>
          <div className="h-5 bg-slate-200 rounded w-2/3 animate-pulse"></div>
        </div>

        {/* 價格骨架 */}
        <div className="h-6 bg-slate-200 rounded w-32 animate-pulse mt-auto"></div>
      </div>
    </div>
  );
};

export default WatchCardSkeleton;
