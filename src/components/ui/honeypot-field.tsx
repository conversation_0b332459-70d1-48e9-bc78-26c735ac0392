/**
 * 蜜罐欄位組件
 * 用於捕捉機器人填寫，對人類用戶完全隱藏
 */

import React from 'react';

interface HoneypotFieldProps {
  name?: string;
  value?: string;
  onChange?: (value: string) => void;
  tabIndex?: number;
}

/**
 * 蜜罐欄位組件
 * 使用多種技術隱藏欄位，避免被機器人檢測到
 */
export function HoneypotField({ 
  name = 'website', 
  value = '', 
  onChange,
  tabIndex = -1 
}: HoneypotFieldProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div
      style={{
        position: 'absolute',
        left: '-9999px',
        top: '-9999px',
        width: '1px',
        height: '1px',
        overflow: 'hidden',
        opacity: 0,
        pointerEvents: 'none',
      }}
      aria-hidden="true"
    >
      <label htmlFor={name} style={{ display: 'none' }}>
        請勿填寫此欄位
      </label>
      <input
        id={name}
        name={name}
        type="text"
        value={value}
        onChange={handleChange}
        tabIndex={tabIndex}
        autoComplete="off"
        style={{
          width: '1px',
          height: '1px',
          border: 'none',
          background: 'transparent',
        }}
      />
    </div>
  );
}

/**
 * 進階蜜罐欄位組件
 * 使用更複雜的隱藏技術，包括假的標籤文字
 */
export function AdvancedHoneypotField({ 
  name = 'company_website', 
  value = '', 
  onChange,
  tabIndex = -1 
}: HoneypotFieldProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <>
      {/* 使用 CSS 類別隱藏的版本 */}
      <div className="sr-only absolute -left-[9999px] -top-[9999px] w-px h-px overflow-hidden opacity-0 pointer-events-none">
        <label htmlFor={name}>
          公司網站（選填）
        </label>
        <input
          id={name}
          name={name}
          type="url"
          value={value}
          onChange={handleChange}
          tabIndex={tabIndex}
          autoComplete="off"
          placeholder="https://www.example.com"
          className="w-px h-px border-0 bg-transparent"
        />
      </div>
      
      {/* 備用隱藏方式 */}
      <input
        name={`${name}_backup`}
        type="text"
        value={value}
        onChange={handleChange}
        tabIndex={tabIndex}
        autoComplete="off"
        style={{
          display: 'none',
          visibility: 'hidden',
        }}
      />
    </>
  );
}

/**
 * 時間戳記隱藏欄位
 * 記錄表單載入時間，用於提交時間驗證
 */
interface TimestampFieldProps {
  name?: string;
  startTime: number;
}

export function TimestampField({ 
  name = 'form_start_time', 
  startTime 
}: TimestampFieldProps) {
  return (
    <input
      name={name}
      type="hidden"
      value={startTime}
      readOnly
    />
  );
}

/**
 * 表單安全 token 欄位
 */
interface SecurityTokenFieldProps {
  name?: string;
  token: string;
}

export function SecurityTokenField({ 
  name = 'security_token', 
  token 
}: SecurityTokenFieldProps) {
  return (
    <input
      name={name}
      type="hidden"
      value={token}
      readOnly
    />
  );
}
