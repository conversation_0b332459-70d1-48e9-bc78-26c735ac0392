'use client';

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

const animatedButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs",
        destructive:
          "bg-destructive text-white shadow-xs focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs dark:bg-input/30 dark:border-input",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs",
        ghost:
          "dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4",
        // 新增奢華變體
        luxury:
          "text-white shadow-lg font-semibold",
        cta:
          "bg-white text-[#2b354d] shadow-lg font-semibold border-0",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        xl: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

interface AnimatedButtonProps
  extends Omit<React.ComponentProps<"button">, 'onDrag' | 'onDragEnd' | 'onDragStart'>,
    VariantProps<typeof animatedButtonVariants> {
  asChild?: boolean;
  enableAnimation?: boolean;
}

const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({
    className,
    variant,
    size,
    asChild = false,
    enableAnimation = true,
    children,
    style,
    ...props
  }, ref) => {
    const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

    // 檢測用戶動畫偏好
    React.useEffect(() => {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);

      const handleChange = (e: MediaQueryListEvent) => {
        setPrefersReducedMotion(e.matches);
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);

    // 動畫配置
    const animationProps = enableAnimation && !prefersReducedMotion ? {
      whileHover: { scale: 1.02 },
      whileTap: { scale: 0.98 },
      transition: { duration: 0.2 },
    } : {};

    if (asChild) {
      return (
        <motion.div
          style={{
            display: 'inline-block',
            willChange: 'transform',
          }}
          {...animationProps}
        >
          <Slot
            ref={ref}
            className={cn(
              animatedButtonVariants({ variant, size, className }),
              // CTA 變體的特殊文字顏色
              variant === 'cta' && 'text-[#2b354d]'
            )}
            style={style}
            {...props}
          >
            {children}
          </Slot>
        </motion.div>
      );
    }

    return (
      <motion.div
        style={{
          display: 'inline-block',
          willChange: 'transform',
        }}
        {...animationProps}
      >
        <button
          ref={ref}
          className={cn(animatedButtonVariants({ variant, size, className }))}
          style={style}
          {...props}
        >
          {/* CTA 按鈕的特殊樣式 */}
          {variant === 'cta' && (
            <span
              className="relative z-10"
              style={{ color: '#2b354d' }}
            >
              {children}
            </span>
          )}

          {/* 其他變體的正常渲染 */}
          {variant !== 'cta' && children}
        </button>
      </motion.div>
    );
  }
);

AnimatedButton.displayName = "AnimatedButton";

export { AnimatedButton, animatedButtonVariants };
export type { AnimatedButtonProps };
