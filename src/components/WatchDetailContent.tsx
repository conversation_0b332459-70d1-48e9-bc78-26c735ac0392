import Link from 'next/link';
import { getServerWatch } from '@/lib/watches-server';
import { notFound } from 'next/navigation';
import WatchDetailClient from './WatchDetailClient';


interface WatchDetailContentProps {
  slug: string;
}

export default async function WatchDetailContent({ slug }: WatchDetailContentProps) {
  const watch = await getServerWatch(slug);

  if (!watch) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 返回按鈕 */}
      <div className="border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-1 sm:py-2">
          <Link
            href="/pre-owned-watches"
            className="inline-flex items-center text-slate-600 hover:text-slate-800 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            返回
          </Link>
        </div>
      </div>

      {/* 手錶詳情內容 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-1 md:py-6 lg:py-8">
        <WatchDetailClient watch={watch} />
      </div>
    </div>
  );
}
