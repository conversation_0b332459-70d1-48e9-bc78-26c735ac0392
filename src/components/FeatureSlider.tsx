"use client";

import Image from "next/image";
import { useRef, useState, useEffect, useCallback } from "react";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { CardMotion, ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { cardEntranceVariants, titleScrollVariants } from "@/lib/motion-config";

import { useIsIOSSafari } from "@/hooks/useBrowserDetection";

interface FeatureImage {
  id: number;
  image: string;
  alt: string;
}

interface FeatureSliderProps {
  images: FeatureImage[];
  title?: string;
}

const FeatureSlider = ({ images, title }: FeatureSliderProps) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { isIOSSafari } = useIsIOSSafari();

  // 燈箱功能
  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  // 鍵盤事件監聽
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') closeLightbox();
      if (e.key === 'ArrowRight') nextImage();
      if (e.key === 'ArrowLeft') prevImage();
    };

    if (lightboxOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [lightboxOpen, nextImage, prevImage]);

  return (
    <section className="py-10 md:py-16 lg:py-18 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        {title && (
          <div className="text-center mb-12 md:mb-16">
            {isIOSSafari ? (
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-6">
                {title}
              </h2>
            ) : (
              <ScrollTriggerMotion
                variants={titleScrollVariants}
              >
                <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-6">
                  {title}
                </h2>
              </ScrollTriggerMotion>
            )}
          </div>
        )}

        {/* Desktop Grid - 針對 iPad Pro 優化佈局 */}
        <div className="hidden md:grid md:grid-cols-3 gap-8 lg:gap-12 xl:gap-16 max-w-6xl mx-auto feature-grid">
          {images.map((item, index) => (
            isIOSSafari ? (
              <div
                key={item.id}
                className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer mx-auto w-full max-w-sm"
                onClick={() => openLightbox(index)}
              >
                <div className="relative h-80 lg:h-96 xl:h-[420px]">
                  <Image
                    src={item.image}
                    alt={item.alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  {/* 點擊提示 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 rounded-full p-3 shadow-lg">
                      <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <CardMotion
                key={item.id}
                index={index}
                variants={cardEntranceVariants}
                className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer mx-auto w-full max-w-sm"
                onClick={() => openLightbox(index)}
              >
                <div className="relative h-80 lg:h-96 xl:h-[420px]">
                  <Image
                    src={item.image}
                    alt={item.alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  {/* 點擊提示 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 rounded-full p-3 shadow-lg">
                      <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </CardMotion>
            )
          ))}
        </div>

        {/* Mobile Slider */}
        <div className="md:hidden relative">
          {/* Scroll Container */}
          <div
            ref={scrollContainerRef}
            className="flex gap-4 overflow-x-auto scrollbar-hide snap-x snap-mandatory"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitOverflowScrolling: 'touch'
            }}
          >
            {images.map((item, index) => (
              <div
                key={item.id}
                className="flex-none w-80 snap-center"
              >
                <div
                  className="relative h-64 rounded-2xl overflow-hidden shadow-lg cursor-pointer group"
                  onClick={() => openLightbox(index)}
                >
                  <Image
                    src={item.image}
                    alt={item.alt}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  {/* 點擊提示 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 rounded-full p-3 shadow-lg">
                      <svg className="w-6 h-6 text-[#2b354d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>


        </div>
      </div>

      {/* Lightbox - 使用與其他組件相同的樣式 */}
      {lightboxOpen && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center" style={{ zIndex: 99999 }}>
          {/* 關閉按鈕 - Safari 兼容性修復 */}
          <button
            onClick={closeLightbox}
            className="text-white hover:text-gray-300 transition-colors"
            style={{
              position: 'fixed',
              top: '16px',
              right: '16px',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            <X size={24} />
          </button>

          {/* 上一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={prevImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              left: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronLeft size={16} />
          </button>

          {/* 下一張按鈕 - Safari 兼容性修復 */}
          <button
            onClick={nextImage}
            className="text-white hover:text-gray-300 transition-all duration-200"
            style={{
              position: 'fixed',
              right: '16px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 999999,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            }}
          >
            <ChevronRight size={16} />
          </button>

          {/* 主圖片 */}
          <div className="relative w-full h-full max-w-4xl max-h-[80vh] mx-4">
            <Image
              src={images[currentImageIndex].image}
              alt={images[currentImageIndex].alt}
              fill
              className="object-contain"
              priority
            />
          </div>

          {/* 圖片指示器 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={cn(
                  "w-3 h-3 rounded-full transition-colors",
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                )}
              />
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
};

export default FeatureSlider;
