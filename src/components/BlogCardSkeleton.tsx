const BlogCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-lg border border-slate-200 h-full w-full flex flex-col">
      {/* 圖片骨架 */}
      <div className="relative w-full h-64 bg-slate-200 animate-pulse flex-shrink-0">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 animate-shimmer"></div>
      </div>

      {/* 內容骨架 */}
      <div className="p-6 flex flex-col flex-grow min-w-0">
        {/* 日期骨架 */}
        <div className="h-4 bg-slate-200 rounded w-24 mb-3 animate-pulse"></div>

        {/* 標題骨架 */}
        <div className="space-y-2 mb-3 min-w-0">
          <div className="h-6 bg-slate-200 rounded w-full animate-pulse"></div>
          <div className="h-6 bg-slate-200 rounded w-3/4 animate-pulse"></div>
        </div>

        {/* 摘要骨架 */}
        <div className="space-y-2 mt-auto min-w-0">
          <div className="h-4 bg-slate-200 rounded w-full animate-pulse"></div>
          <div className="h-4 bg-slate-200 rounded w-full animate-pulse"></div>
          <div className="h-4 bg-slate-200 rounded w-2/3 animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

export default BlogCardSkeleton;
