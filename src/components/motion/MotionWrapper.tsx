'use client';

import { motion, useInView } from 'motion/react';
import { useRef } from 'react';
import { cn } from '@/lib/utils';
import { useAnimationCompatibility } from '@/hooks/useBrowserDetection';

interface MotionWrapperProps {
  children: React.ReactNode;
  className?: string;
  variants?: Record<string, unknown>;
  initial?: string | Record<string, unknown>;
  animate?: string | Record<string, unknown>;
  whileHover?: string | Record<string, unknown>;
  whileTap?: string | Record<string, unknown>;
  custom?: number | string | Record<string, unknown>;
  delay?: number;
  duration?: number;
  useScrollTrigger?: boolean;
  triggerOnce?: boolean;
  threshold?: number;
  as?: keyof React.JSX.IntrinsicElements;
  style?: React.CSSProperties;
  onClick?: () => void;
}



/**
 * 通用動畫包裝器組件
 * 支援滾動觸發、響應式動畫和無障礙設計
 * 包含 iOS Safari 兼容性修復
 */
export default function MotionWrapper({
  children,
  className,
  variants,
  initial = 'hidden',
  animate = 'visible',
  whileHover,
  whileTap,
  custom,
  delay = 0,
  duration,
  useScrollTrigger = false,
  triggerOnce = true,
  threshold = 0.1,
  as = 'div',
  style,
  onClick,
}: MotionWrapperProps) {
  const ref = useRef(null);
  const { shouldUseSimpleAnimation, isIOSSafari: isIOSSafariBrowser } = useAnimationCompatibility();

  // 使用兼容的 useInView 配置
  const isInView = useInView(ref, {
    once: triggerOnce,
    // iOS Safari 兼容的 margin 語法
    margin: isIOSSafariBrowser ? '0px' : '-10% 0px -10% 0px',
    amount: threshold,
  });

  const animationProps = shouldUseSimpleAnimation
    ? {
        initial: { opacity: 0, y: 0 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.2 },
      }
    : {
        variants,
        initial,
        animate: useScrollTrigger ? (isInView ? animate : initial) : animate,
        whileHover,
        whileTap,
        custom,
        transition: duration ? { duration, delay } : { delay },
      };

  const MotionComponent = motion[as as keyof typeof motion] as React.ComponentType<
    React.ComponentProps<typeof as> & {
      variants?: Record<string, unknown>;
      initial?: string | Record<string, unknown>;
      animate?: string | Record<string, unknown>;
      whileHover?: string | Record<string, unknown>;
      whileTap?: string | Record<string, unknown>;
      custom?: number | string | Record<string, unknown>;
      transition?: Record<string, unknown>;
      style?: React.CSSProperties;
      className?: string;
      onClick?: () => void;
    }
  >;

  // iOS Safari 兼容的樣式
  const compatibleStyle = {
    // 在 iOS Safari 上避免使用 willChange
    ...(isIOSSafariBrowser ? {} : { willChange: 'transform, opacity' }),
    ...style,
  };

  return (
    <MotionComponent
      ref={ref}
      className={cn(className)}
      style={compatibleStyle}
      onClick={onClick}
      {...animationProps}
    >
      {children}
    </MotionComponent>
  );
}

/**
 * 滾動觸發動畫的簡化組件
 */
export function ScrollTriggerMotion({
  children,
  className,
  variants,
  delay = 0,
  ...props
}: Omit<MotionWrapperProps, 'useScrollTrigger'>) {
  return (
    <MotionWrapper
      className={className}
      variants={variants}
      useScrollTrigger={true}
      delay={delay}
      {...props}
    >
      {children}
    </MotionWrapper>
  );
}

/**
 * 按鈕動畫的簡化組件
 */
export function ButtonMotion({
  children,
  className,
  variants,
  onClick,
  ...props
}: Omit<MotionWrapperProps, 'as'>) {
  return (
    <MotionWrapper
      as="button"
      className={className}
      variants={variants}
      onClick={onClick}
      {...props}
    >
      {children}
    </MotionWrapper>
  );
}

/**
 * 卡片動畫的簡化組件
 */
export function CardMotion({
  children,
  className,
  variants,
  index = 0,
  ...props
}: Omit<MotionWrapperProps, 'custom'> & { index?: number }) {
  return (
    <MotionWrapper
      className={className}
      variants={variants}
      custom={index}
      useScrollTrigger={true}
      {...props}
    >
      {children}
    </MotionWrapper>
  );
}

/**
 * 容器動畫的簡化組件（用於包含多個子元素）
 */
export function ContainerMotion({
  children,
  className,
  variants,
  ...props
}: Omit<MotionWrapperProps, 'useScrollTrigger'>) {
  return (
    <MotionWrapper
      className={className}
      variants={variants}
      useScrollTrigger={true}
      {...props}
    >
      {children}
    </MotionWrapper>
  );
}
