'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  onClick?: () => void;
  loading?: 'lazy' | 'eager';
}

// 檢測是否為 iOS Safari
function isIOSSafari(): boolean {
  if (typeof window === 'undefined') return false;

  const userAgent = window.navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);

  return isIOS && isSafari;
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  className = '',
  sizes,
  priority = false,
  onClick,
  loading = 'lazy'
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isIOSSafariBrowser, setIsIOSSafariBrowser] = useState(false);

  // 檢測 iOS Safari
  useEffect(() => {
    setIsIOSSafariBrowser(isIOSSafari());
  }, []);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div 
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        onClick={onClick}
      >
        <div className="text-center text-gray-400">
          <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-xs">圖片載入失敗</p>
        </div>
      </div>
    );
  }

  // 檢查是否已經包含 object-fit 相關的 className
  const hasObjectFit = className.includes('object-cover') || className.includes('object-contain') || className.includes('object-fill');
  const defaultObjectFit = fill && !hasObjectFit ? 'object-contain' : '';

  // iOS Safari 兼容的圖片屬性
  const imageProps = isIOSSafariBrowser
    ? {
        // iOS Safari 兼容模式：移除可能有問題的屬性
        src,
        alt,
        width,
        height,
        fill,
        className: `transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'} ${defaultObjectFit} ${className}`,
        sizes,
        priority,
        onLoad: handleLoad,
        onError: handleError,
        quality: 75, // 降低品質以提高兼容性
      }
    : {
        // 標準模式：使用所有功能
        src,
        alt,
        width,
        height,
        fill,
        className: `transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'} ${defaultObjectFit} ${className}`,
        sizes,
        priority,
        loading,
        onLoad: handleLoad,
        onError: handleError,
        quality: 85,
        placeholder: "blur" as const,
        blurDataURL: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
      };

  // 如果使用 fill 屬性，不要添加額外的包裝器
  if (fill) {
    return (
      <div className="absolute inset-0" onClick={onClick}>
        {/* 載入指示器 */}
        {isLoading && (
          <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
          </div>
        )}

        {/* 圖片 */}
        <Image {...imageProps} alt={alt} />
      </div>
    );
  }

  // 非 fill 模式使用包裝器
  return (
    <div className={`relative ${className}`} onClick={onClick}>
      {/* 載入指示器 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
        </div>
      )}

      {/* 圖片 */}
      <Image {...imageProps} alt={alt} />
    </div>
  );
}
