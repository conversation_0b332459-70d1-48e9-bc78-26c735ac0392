'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

interface LazyImageProps {
  src: string;
  alt: string;
  fill?: boolean;
  className?: string;
  sizes?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  placeholder?: string;
  quality?: number;
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const LazyImage = ({
  src,
  alt,
  fill = false,
  className = '',
  sizes,
  width,
  height,
  priority = false,
  placeholder = '/images/placeholder.jpg',
  quality = 85,
  blurDataURL,
  onLoad: onLoadProp,
  onError: onErrorProp
}: LazyImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (priority) {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoadProp?.();
  };

  const handleError = () => {
    setError(true);
    setIsLoaded(true);
    onErrorProp?.();
  };

  return (
    <div ref={imgRef} className={fill ? "absolute inset-0" : `relative ${className}`}>
      {/* 載入中的佔位符 */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-slate-200 animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 animate-shimmer"></div>
        </div>
      )}

      {/* 實際圖片 */}
      {isInView && (
        <Image
          src={error ? placeholder : src}
          alt={alt}
          fill={fill}
          width={!fill ? width : undefined}
          height={!fill ? height : undefined}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          } ${fill ? className : ''}`}
          sizes={sizes}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          quality={quality}
          placeholder={blurDataURL ? 'blur' : 'empty'}
          blurDataURL={blurDataURL}
        />
      )}
    </div>
  );
};

export default LazyImage;
