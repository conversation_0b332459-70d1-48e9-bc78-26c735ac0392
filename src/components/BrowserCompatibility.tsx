'use client';

import { useEffect } from 'react';
import { addBrowserClasses, detectBrowser } from '@/utils/browserDetection';

/**
 * 瀏覽器兼容性初始化組件
 * 在應用啟動時檢測瀏覽器並添加相應的 CSS 類名
 */
export default function BrowserCompatibility() {
  useEffect(() => {
    // 添加瀏覽器特定的 CSS 類名
    addBrowserClasses();
    
    // 檢測瀏覽器並在控制台輸出信息（開發和生產環境都輸出以便調試）
    const browser = detectBrowser();
    console.log('Browser detection:', browser);
    console.log('User Agent:', navigator.userAgent);
    console.log('Platform:', navigator.platform);
    console.log('Max Touch Points:', navigator.maxTouchPoints);
    console.log('Body classes:', document.body.className);

    if (browser.isIOSSafari) {
      console.warn('iOS Safari detected - using compatibility mode');
      // 強制添加 iOS Safari 類名以確保樣式生效
      document.body.classList.add('ios-safari-detected');
      console.log('Added ios-safari-detected class');
    }
  }, []);

  // 這個組件不渲染任何內容
  return null;
}
