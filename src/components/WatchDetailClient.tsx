'use client';

import { useState } from 'react';
import { Watch } from '@/types/watch';
import OptimizedImage from './OptimizedImage';

import { X, ChevronLeft, ChevronRight } from 'lucide-react';

interface WatchDetailClientProps {
  watch: Watch;
}

export default function WatchDetailClient({ watch }: WatchDetailClientProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);

  // 處理圖片陣列 - 從 image 和 thumbnail 欄位解析
  const parseImageUrls = (imageString: string): string[] => {
    if (!imageString) return [];
    return imageString
      .split(/[,\n\r]+/)
      .map(url => url.trim())
      .filter(url => url && url.startsWith('http'));
  };

  const images = [
    ...parseImageUrls(watch.image || ''),
    ...parseImageUrls(watch.thumbnail || ''),
  ].filter((img, index, arr) => img && arr.indexOf(img) === index); // 去重

  // 格式化價格顯示
  const formatPrice = (price: number) => {
    return `NT$${new Intl.NumberFormat('zh-TW').format(price)}`;
  };

  // 處理立即諮詢點擊
  const handleConsultClick = () => {
    if (watch?.cta) {
      window.open(watch.cta, '_blank');
    }
  };

  // 處理圖片點擊
  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
    setIsLightboxOpen(true);
  };

  // 關閉燈箱
  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  // 上一張圖片
  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  // 下一張圖片
  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
      {/* 圖片展示區域 */}
      <div className="w-full">
        {/* 主圖片 */}
        {images.length > 0 && (
          <div className="relative w-full watch-detail-main-image rounded-md overflow-hidden">
            <OptimizedImage
              src={images[currentImageIndex]}
              alt={`${watch.brand} ${watch.productName}`}
              width={800}
              height={600}
              className="w-full h-auto object-cover cursor-pointer hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={true}
              loading="eager"
              onClick={() => handleImageClick(currentImageIndex)}
            />
          </div>
        )}

        {/* 縮圖列表 */}
        {images.length > 1 && (
          <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-4 xl:grid-cols-6 w-full watch-detail-thumbnails">
            {images.slice(0, 12).map((image, index) => (
              <div
                key={index}
                className={`relative watch-detail-thumbnail rounded-md overflow-hidden cursor-pointer border transition-all ${
                  index === currentImageIndex ? 'border-[#2b354d]' : 'border-transparent hover:border-gray-300'
                }`}
                onClick={() => setCurrentImageIndex(index)}
              >
                <OptimizedImage
                  src={image}
                  alt={`${watch.brand} ${watch.productName} - 圖片 ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 25vw, (max-width: 1200px) 12.5vw, 8.33vw"
                  priority={index < 4}
                  loading={index < 4 ? 'eager' : 'lazy'}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 手錶資訊 */}
      <div>
        <div className="lg:pl-8">
          {/* 品牌 */}
          <div className="mb-3">
            <h2 className="text-xl font-bold" style={{ color: '#2b354d' }}>
              {watch.brand}
            </h2>
          </div>

          {/* 產品名稱 */}
          <div className="mb-4">
            <h1 className="text-2xl font-bold" style={{ color: '#2b354d' }}>
              {watch.productName}
            </h1>
          </div>

          {/* 價格 */}
          {watch.price && (
            <div className="mb-6">
              <p className="text-2xl font-bold" style={{ color: '#2b354d' }}>
                {formatPrice(watch.price)}
              </p>
            </div>
          )}

          {/* 詳細描述 */}
          {watch.listingDescription && (
            <div className="mb-6">
              {/* 檢查是否包含 HTML 標籤 */}
              {watch.listingDescription.includes('<') ? (
                // 如果包含 HTML 標籤，使用原本的方式
                <div
                  className="text-sm leading-relaxed watch-description"
                  style={{ color: '#2b354d' }}
                  dangerouslySetInnerHTML={{ __html: watch.listingDescription }}
                />
              ) : (
                // 如果是純文字，處理換行
                <div className="text-sm leading-relaxed" style={{ color: '#2b354d' }}>
                  {watch.listingDescription.split(/\r?\n/).map((line, index) => {
                    const trimmedLine = line.trim();
                    if (!trimmedLine) return null;

                    // 檢查是否為網址（更完整的檢測）
                    const urlRegex = /^https?:\/\/.+/i;
                    const isUrl = urlRegex.test(trimmedLine);

                    return (
                      <div key={index} className="mb-1">
                        {isUrl ? (
                          <a
                            href={trimmedLine}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm underline hover:opacity-80 break-all"
                            style={{ color: '#2b354d' }}
                          >
                            {trimmedLine}
                          </a>
                        ) : (
                          <span className="font-medium">{trimmedLine}</span>
                        )}
                      </div>
                    );
                  }).filter(Boolean)}
                </div>
              )}
            </div>
          )}

          {/* 基本資訊 - Tag 形式 */}
          <div className="mb-8 flex flex-wrap gap-2">
            {watch.boxAndPaper && (
              <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                {watch.boxAndPaper}
              </span>
            )}
            {watch.movement && (
              <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                {watch.movement}
              </span>
            )}
            {watch.caseSize && (
              <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                {watch.caseSize}
              </span>
            )}
          </div>

          {/* CTA 按鈕 */}
          {watch.cta && (
            <div className="mb-8">
              <button
                onClick={handleConsultClick}
                className="w-full py-3 px-6 rounded-lg font-medium transition-colors cursor-pointer"
                style={{
                  backgroundColor: '#2b354d',
                  color: '#ffffff'
                }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                立即諮詢
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 燈箱 */}
      {isLightboxOpen && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center" style={{ zIndex: 99999 }}>
          <div className="relative max-w-4xl max-h-full p-4">
            {/* 關閉按鈕 */}
            <button
              onClick={closeLightbox}
              className="text-white hover:text-gray-300 transition-colors"
              style={{
                position: 'fixed',
                top: '16px',
                right: '16px',
                zIndex: 999999,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderRadius: '50%',
                width: '48px',
                height: '48px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              <X size={24} />
            </button>

            {/* 圖片 */}
            <div className="relative">
              <OptimizedImage
                src={images[currentImageIndex]}
                alt={`${watch.brand} ${watch.productName}`}
                width={800}
                height={600}
                className="max-w-full max-h-[80vh] object-contain"
                priority={true}
                loading="eager"
              />

              {/* 導航按鈕 */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={previousImage}
                    className="text-white hover:text-gray-300 transition-all duration-200"
                    style={{
                      position: 'fixed',
                      left: '16px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 999999,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '50%',
                      width: '48px',
                      height: '48px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
                    }}
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <button
                    onClick={nextImage}
                    className="text-white hover:text-gray-300 transition-all duration-200"
                    style={{
                      position: 'fixed',
                      right: '16px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 999999,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '50%',
                      width: '48px',
                      height: '48px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
                    }}
                  >
                    <ChevronRight size={16} />
                  </button>
                </>
              )}
            </div>

            {/* 圖片計數 */}
            {images.length > 1 && (
              <div className="text-center text-white mt-4 text-lg">
                {currentImageIndex + 1} / {images.length}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
