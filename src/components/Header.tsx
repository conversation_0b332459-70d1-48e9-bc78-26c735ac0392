"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Menu, X, Droplets, Bookmark, Watch, Wrench, HelpCircle } from 'lucide-react';
import { AnimatedButton } from '@/components/ui/animated-button';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const navigationItems = [
    { href: "https://aquapure.weaven.co", label: "AQUA PURE", icon: Droplets, external: true },
    { href: "/blog", label: "精選文章", icon: Bookmark, external: false },
    { href: "/pre-owned-watches", label: "錶款選購", icon: Watch, external: false },
    { href: "/movement-assembling-booking", label: "錶匠體驗", icon: Wrench, external: false },
    { href: "/support", label: "常見問題", icon: HelpCircle, external: false },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/10 bg-white">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo - Always on the left */}
        <div className="flex items-center">
          <Link
            href="/"
            className="flex items-center hover:opacity-80 transition-opacity"
            onClick={closeMobileMenu}
          >
            <Image
              src="/images/logo.png"
              alt="Weaven Logo"
              width={120}
              height={40}
              className="h-8 w-auto md:h-10"
              priority
            />
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center space-x-1">
          {navigationItems.map((item) => {
            const IconComponent = item.icon;
            return item.external ? (
              <a
                key={item.href}
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className={cn(
                  "inline-flex h-10 items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200",
                  "hover:bg-gray-100 hover:text-gray-800 focus:bg-gray-100 focus:outline-none",
                  "gap-2"
                )}
                style={{ color: '#2b354d' }}
              >
                <IconComponent
                  className="h-4 w-4 flex-shrink-0"
                  style={{ color: '#2b354d' }}
                />
                <span>{item.label}</span>
              </a>
            ) : (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "inline-flex h-10 items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-all duration-200",
                  "hover:bg-gray-100 hover:text-gray-800 focus:bg-gray-100 focus:outline-none",
                  "gap-2"
                )}
                style={{ color: '#2b354d' }}
              >
                <IconComponent
                  className="h-4 w-4 flex-shrink-0"
                  style={{ color: '#2b354d' }}
                />
                <span>{item.label}</span>
              </Link>
            );
          })}

          {/* 錶盒預約 Button */}
          <AnimatedButton
            asChild
            variant="default"
            size="default"
            className={cn(
              "ml-6 h-10 text-sm font-medium rounded-md px-4 py-2"
            )}
            style={{
              backgroundColor: '#2b354d',
              color: 'white',
              border: 'none'
            }}
          >
            <Link href="/pangea-booking" className="flex items-center h-full">
              錶盒預約
            </Link>
          </AnimatedButton>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex lg:hidden">
          <button
            onClick={toggleMobileMenu}
            className={cn(
              "inline-flex items-center justify-center rounded-md p-2 text-foreground",
              "hover:bg-accent/10 hover:text-accent focus:bg-accent/10 focus:text-accent focus:outline-none",
              "transition-colors duration-200"
            )}
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle navigation menu"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden border-t border-border/10 bg-white">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-2">
              {navigationItems.map((item) => {
                const IconComponent = item.icon;
                return item.external ? (
                  <a
                    key={item.href}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={closeMobileMenu}
                    className={cn(
                      "flex items-center gap-3 rounded-md px-4 py-3 text-base font-medium transition-all duration-200",
                      "hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    )}
                    style={{ color: '#2b354d' }}
                  >
                    <IconComponent className="h-5 w-5 transition-colors duration-200" style={{ color: '#2b354d' }} />
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={closeMobileMenu}
                    className={cn(
                      "flex items-center gap-3 rounded-md px-4 py-3 text-base font-medium transition-all duration-200",
                      "hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    )}
                    style={{ color: '#2b354d' }}
                  >
                    <IconComponent className="h-5 w-5 transition-colors duration-200" style={{ color: '#2b354d' }} />
                    {item.label}
                  </Link>
                );
              })}

              {/* Mobile 錶盒預約 Button */}
              <Link
                href="/pangea-booking"
                onClick={closeMobileMenu}
                className={cn(
                  "flex items-center justify-center rounded-md px-4 py-3 text-base font-medium transition-all duration-200 mt-4",
                  "hover:opacity-90 focus:opacity-90 focus:outline-none"
                )}
                style={{
                  backgroundColor: '#2b354d',
                  color: 'white'
                }}
              >
                錶盒預約
              </Link>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
