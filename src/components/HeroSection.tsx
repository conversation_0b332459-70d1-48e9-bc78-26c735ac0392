"use client";

import VideoBackground from "./VideoBackground";
import { ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { detectBrowser } from "@/utils/browserDetection";

interface HeroSectionProps {
  title: string;
  subtitle: string;
}

const HeroSection = ({
  title,
  subtitle
}: HeroSectionProps) => {
  const [isIOSSafari, setIsIOSSafari] = useState(false);

  useEffect(() => {
    const browser = detectBrowser();
    setIsIOSSafari(browser.isIOSSafari);
    if (browser.isIOSSafari) {
      console.log('HeroSection: iOS Safari detected, applying fixes');
    }
  }, []);

  // iOS Safari 特定的標題樣式
  const iosSafariTitleStyle = isIOSSafari ? {
    color: '#ffffff',
    margin: '0 auto',
    display: 'block',
    width: '100%',
    maxWidth: '100%',
    transform: 'none',
    WebkitTransform: 'none',
    position: 'static' as const,
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } : {
    color: '#ffffff',
    margin: '0 auto',
    display: 'block',
    width: '100%',
    maxWidth: '100%'
  };

  return (
    <section className="relative flex h-[50vh] md:h-[60vh] lg:h-screen items-center justify-center text-white">
      <VideoBackground src="/videos/pangea-video.mp4" />

      {/* Hero Content */}
      <div className="z-10 w-full text-center px-6">
        {/* Main Title */}
        <h1
          className="hero-title text-3xl md:text-5xl font-extrabold tracking-tight mb-6 text-center"
          style={iosSafariTitleStyle}
        >
          {title}
        </h1>

        {/* Subtitle */}
        <h2 className="text-lg md:text-2xl font-light" style={{ color: '#ffffff', opacity: 0.9 }}>
          {subtitle}
        </h2>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <ChevronDown className="w-6 h-6 opacity-70" style={{ color: '#ffffff' }} />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
