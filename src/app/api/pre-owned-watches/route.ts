import { NextResponse } from 'next/server';
import { getCachedWatchListData } from '@/lib/cache-implementation';
import { transformWatchListData, type WatchListItem } from '@/types/watch';

/**
 * GET /api/pre-owned-watches
 * 獲取所有手錶資料（優化版本，只讀取列表頁面需要的欄位）
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const brand = searchParams.get('brand');
    const page = parseInt(searchParams.get('page') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // 使用快取版本的讀取函數，只獲取列表頁面需要的欄位
    const rawData = await getCachedWatchListData();

    if (!rawData || rawData.length === 0) {
      return NextResponse.json({
        watches: [],
        brands: [],
        total: 0,
        hasMore: false
      });
    }

    // 跳過標題列，轉換資料為 WatchListItem
    const allWatches: WatchListItem[] = rawData
      .slice(1) // 跳過標題列
      .map((row, index) => transformWatchListData(row, index + 1))
      .filter(watch => watch.productName && watch.brand); // 過濾空資料

    // 如果有品牌篩選，進行篩選
    let filteredWatches = allWatches;
    if (brand && brand !== 'all') {
      filteredWatches = allWatches.filter(watch =>
        watch.brand.toLowerCase() === brand.toLowerCase()
      );
    }

    // 分頁處理
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const watches = filteredWatches.slice(startIndex, endIndex);
    const hasMore = endIndex < filteredWatches.length;

    // 獲取所有品牌列表
    const brands = [...new Set(allWatches.map(watch => watch.brand))].sort();

    const response = NextResponse.json({
      watches,
      brands,
      total: filteredWatches.length,
      hasMore,
      page,
      pageSize
    });

    return response;

  } catch (error) {
    console.error('獲取手錶資料失敗:', error);
    return NextResponse.json(
      { error: '無法獲取手錶資料' },
      { status: 500 }
    );
  }
}
