import { NextResponse } from 'next/server';
import { getCachedBlogListData } from '@/lib/cache-implementation';
import { transformBlogListData, type BlogListItem } from '@/types/blog';

/**
 * GET /api/blog
 * 獲取所有部落格文章（優化版本，只讀取列表頁面需要的欄位）
 */

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // 使用快取版本的讀取函數，只獲取列表頁面需要的欄位
    const rawData = await getCachedBlogListData();

    if (!rawData || rawData.length === 0) {
      return NextResponse.json({
        posts: [],
        total: 0,
        hasMore: false,
        message: '沒有找到部落格資料，請檢查 Google Sheets 設定'
      });
    }

    // 跳過標題列，轉換資料為 BlogListItem
    const allPosts: BlogListItem[] = rawData
      .slice(1) // 跳過標題列
      .map((row, index) => transformBlogListData(row, index + 1))
      .filter(post => post.title && post.slug); // 過濾空資料

    // 按發布日期排序（最新的在前）
    allPosts.sort((a, b) => b.publishDate.getTime() - a.publishDate.getTime());

    // 分頁處理
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const posts = allPosts.slice(startIndex, endIndex);
    const hasMore = endIndex < allPosts.length;

    const response = NextResponse.json({
      posts,
      total: allPosts.length,
      hasMore,
      page,
      pageSize
    });

    return response;

  } catch (error) {
    console.error('獲取部落格文章失敗:', error);
    console.error('錯誤詳情:', error instanceof Error ? error.message : String(error));

    return NextResponse.json(
      {
        error: '無法獲取部落格文章',
        details: error instanceof Error ? error.message : String(error),
        sheetId: process.env.GOOGLE_BLOG_SHEET_ID || process.env.GOOGLE_SHEET_ID,
        suggestions: [
          '1. 確認 Google Sheets 中有名為 "Blog" 的工作表',
          '2. 確認服務帳戶有存取權限',
          '3. 檢查 .env.local 中的 GOOGLE_SHEET_ID 設定',
          '4. 嘗試訪問 /test-blog 頁面進行詳細診斷'
        ]
      },
      { status: 500 }
    );
  }
}
