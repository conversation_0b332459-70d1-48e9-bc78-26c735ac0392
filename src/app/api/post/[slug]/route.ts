import { NextResponse } from 'next/server';
import { getCachedBlogDetailData } from '@/lib/cache-implementation';
import { transformBlogData, type BlogPost } from '@/types/blog';

// 快取版本的資料讀取函數已整合到 cache-implementation.ts 中

/**
 * GET /api/post/[slug]
 * 獲取單一部落格文章
 */
export async function GET(
  _request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // 從快取讀取 Google Sheets 資料
    const rawData = await getCachedBlogDetailData();
    
    if (!rawData || rawData.length === 0) {
      return NextResponse.json(
        { error: '找不到部落格文章' },
        { status: 404 }
      );
    }

    // 跳過標題列，轉換資料
    const posts: BlogPost[] = rawData
      .slice(1) // 跳過標題列
      .map((row, index) => transformBlogData(row, index + 1))
      .filter(post => post.title && post.slug); // 過濾空資料

    // 尋找指定的文章（支援 slug 或 seoSlug）
    const post = posts.find(p => p.slug === slug || p.seoSlug === slug);

    if (!post) {
      return NextResponse.json(
        { error: '找不到指定的文章' },
        { status: 404 }
      );
    }

    const response = NextResponse.json({ post });
    return response;

  } catch (error) {
    console.error('獲取部落格文章詳情失敗:', error);
    return NextResponse.json(
      { error: '無法獲取部落格文章詳情' },
      { status: 500 }
    );
  }
}
