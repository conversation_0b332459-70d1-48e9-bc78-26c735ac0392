'use client';

import { useState, useCallback, useEffect } from 'react';
import WatchCard from '@/components/WatchCard';
import WatchCardSkeleton from '@/components/WatchCardSkeleton';
import { WatchesResponse } from '@/types/watch';
import { useLazyLoading } from '@/hooks/useLazyLoading';

const WatchesPage = () => {
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedBrand, setSelectedBrand] = useState<string>('all');

  // 獲取手錶資料的函數
  const fetchWatchesData = useCallback(async (page: number, pageSize: number) => {
    const url = selectedBrand === 'all'
      ? `/api/pre-owned-watches?page=${page}&pageSize=${pageSize}`
      : `/api/pre-owned-watches?brand=${encodeURIComponent(selectedBrand)}&page=${page}&pageSize=${pageSize}`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('無法獲取手錶資料');
    }

    const data: WatchesResponse = await response.json();

    // 更新品牌列表（只在第一次載入時）
    if (page === 0) {
      setBrands(data.brands);
    }

    return {
      items: data.watches,
      total: data.total,
      hasMore: data.hasMore
    };
  }, [selectedBrand]);

  // 使用 lazy loading hook
  const {
    items: watches,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isLoadingMore
  } = useLazyLoading({
    fetchFunction: fetchWatchesData,
    pageSize: 9,
    initialLoad: true
  });



  // 品牌篩選變更
  const handleBrandChange = (brand: string) => {
    setSelectedBrand(brand);
  };

  // 當品牌選擇改變時重新載入
  useEffect(() => {
    refresh();
  }, [selectedBrand, refresh]);

  if (error) {
    return (
      <div className="min-h-screen bg-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl font-light mb-4" style={{ color: '#2b354d' }}>載入失敗</h1>
          <p className="mb-8" style={{ color: '#2b354d' }}>{error}</p>
          <button
            onClick={refresh}
            className="px-6 py-3 rounded-lg transition-colors"
            style={{ backgroundColor: '#2b354d', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            重新載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4" style={{ color: '#2b354d' }}>精選錶款</h1>
            <p className="text-lg" style={{ color: '#2b354d' }}>對任何錶款有興趣歡迎直接私訊粉專洽談

</p>
          </div>
        </div>
      </div>

      {/* 品牌篩選器 */}
      <section className="py-8 bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-2">
            <button
              onClick={() => handleBrandChange('all')}
              className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                selectedBrand === 'all'
                  ? 'shadow-lg'
                  : 'bg-slate-100 hover:bg-slate-200'
              }`}
              style={{
                backgroundColor: selectedBrand === 'all' ? '#2b354d' : undefined,
                color: selectedBrand === 'all' ? '#ffffff' : '#2b354d'
              }}
            >
              全部品牌
            </button>
            {brands.map((brand) => (
              <button
                key={brand}
                onClick={() => handleBrandChange(brand)}
                className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                  selectedBrand === brand
                    ? 'shadow-lg'
                    : 'bg-slate-100 hover:bg-slate-200'
                }`}
                style={{
                  backgroundColor: selectedBrand === brand ? '#2b354d' : undefined,
                  color: selectedBrand === brand ? '#ffffff' : '#2b354d'
                }}
              >
                {brand}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* 手錶列表 */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading && watches.length === 0 ? (
            // 初始載入骨架屏
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
              {Array.from({ length: 9 }).map((_, index) => (
                <WatchCardSkeleton key={index} />
              ))}
            </div>
          ) : watches.length === 0 ? (
            <div className="text-center py-20">
              <h3 className="text-2xl font-light mb-4" style={{ color: '#2b354d' }}>
                {selectedBrand === 'all' ? '暫無手錶資料' : `暫無 ${selectedBrand} 品牌的手錶`}
              </h3>
              <p style={{ color: '#2b354d' }}>請稍後再試或選擇其他品牌</p>
            </div>
          ) : (
            <>
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
                {watches.map((watch) => (
                  <WatchCard key={watch.id} watch={watch} />
                ))}
              </div>

              {/* 載入更多內容的骨架屏 */}
              {isLoadingMore && (
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 watch-card-grid mt-8">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <WatchCardSkeleton key={`loading-${index}`} />
                  ))}
                </div>
              )}

              {/* 查看更多按鈕 */}
              {hasMore && !isLoadingMore && (
                <div className="flex items-center justify-center mt-12">
                  <button
                    onClick={loadMore}
                    className="px-8 py-3 rounded-lg border border-slate-300 transition-all duration-200 hover:border-slate-400 hover:shadow-md"
                    style={{
                      backgroundColor: '#ffffff',
                      color: '#2b354d',
                      fontWeight: '500'
                    }}
                  >
                    查看更多手錶
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>
    </div>
  );
};

export default WatchesPage;
