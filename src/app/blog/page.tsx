'use client';

import { useCallback, useEffect } from 'react';
import BlogCard from '@/components/BlogCard';
import BlogCardSkeleton from '@/components/BlogCardSkeleton';
import { BlogResponse } from '@/types/blog';
import { useLazyLoading } from '@/hooks/useLazyLoading';
import { smartPreloadImages } from '@/utils/imagePreloader';

const BlogPage = () => {
  // 獲取部落格文章資料的函數
  const fetchBlogPosts = useCallback(async (page: number, pageSize: number) => {
    const response = await fetch(`/api/blog?page=${page}&pageSize=${pageSize}`);
    if (!response.ok) {
      throw new Error('無法獲取部落格文章');
    }
    const data: BlogResponse = await response.json();

    // 轉換日期格式
    const items = data.posts.map(post => ({
      ...post,
      publishDate: new Date(post.publishDate)
    }));

    return {
      items,
      total: data.total,
      hasMore: data.hasMore
    };
  }, []);

  // 使用 lazy loading hook
  const {
    items: posts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isLoadingMore
  } = useLazyLoading({
    fetchFunction: fetchBlogPosts,
    pageSize: 9,
    initialLoad: true
  });

  // 預載入圖片
  useEffect(() => {
    if (posts.length > 0) {
      // 提取前幾篇文章的縮圖 URL
      const imageUrls = posts
        .slice(0, 6) // 只預載入前 6 張圖片
        .map(post => post.thumbnail)
        .filter(Boolean) as string[];

      if (imageUrls.length > 0) {
        smartPreloadImages(imageUrls).then(results => {
          const successCount = results.filter(r => r.success).length;
          console.log(`預載入完成: ${successCount}/${results.length} 張圖片`);
        });
      }
    }
  }, [posts]);



  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-light mb-4" style={{ color: '#2b354d' }}>載入失敗</h1>
          <p className="mb-8" style={{ color: '#2b354d' }}>{error}</p>
          <button
            onClick={refresh}
            className="px-6 py-3 rounded-lg transition-colors"
            style={{ backgroundColor: '#2b354d', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            重新載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4" style={{ color: '#2b354d' }}>Weaven Blog</h1>
            <p className="text-lg" style={{ color: '#2b354d' }}>讓享受機械錶變簡單</p>
          </div>
        </div>
      </div>

      {/* 文章列表 */}
      <div className="py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading && posts.length === 0 ? (
            // 初始載入骨架屏
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 blog-card-grid">
              {Array.from({ length: 9 }).map((_, index) => (
                <BlogCardSkeleton key={index} />
              ))}
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-12">
              <p style={{ color: '#2b354d' }}>目前沒有文章</p>
            </div>
          ) : (
            <>
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 blog-card-grid">
                {posts.map((post) => (
                  <BlogCard key={post.slug} post={post} />
                ))}
              </div>

              {/* 載入更多內容的骨架屏 */}
              {isLoadingMore && (
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 blog-card-grid mt-8">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <BlogCardSkeleton key={`loading-${index}`} />
                  ))}
                </div>
              )}

              {/* 查看更多按鈕 */}
              {hasMore && !isLoadingMore && (
                <div className="flex items-center justify-center mt-12">
                  <button
                    onClick={loadMore}
                    className="px-8 py-3 rounded-lg border border-slate-300 transition-all duration-200 hover:border-slate-400 hover:shadow-md"
                    style={{
                      backgroundColor: '#ffffff',
                      color: '#2b354d',
                      fontWeight: '500'
                    }}
                  >
                    查看更多文章
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogPage;
