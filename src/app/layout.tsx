import type { Metadata, Viewport } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import GTMContainer from '@/components/GTMContainer';
import BrowserCompatibility from '@/components/BrowserCompatibility';
import { DEFAULT_METADATA } from '@/config/seo-config';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = DEFAULT_METADATA;

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5, // 允許適度縮放，避免 iOS Safari 渲染問題
  userScalable: true, // 改為允許縮放，提高 iOS Safari 兼容性
  viewportFit: 'cover', // 支援 iPhone X 等設備的安全區域
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-TW">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <BrowserCompatibility />
        <div className="flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
        <GTMContainer />
      </body>
    </html>
  );
}
