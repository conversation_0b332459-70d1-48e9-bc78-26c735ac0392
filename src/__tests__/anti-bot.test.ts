/**
 * 防機器人功能測試
 */

import { 
  validateHoneypot, 
  validateSubmissionTime, 
  validateDuplicateSubmission,
  analyzeUserAgent,
  validateSubmission,
  generateFormToken,
  validateFormToken,
  DEFAULT_ANTI_BOT_CONFIG
} from '@/lib/anti-bot';

import {
  checkRateLimit,
  recordRequest,
  checkEmailRateLimit,
  recordEmailRequest,
  DEFAULT_RATE_LIMIT_CONFIG
} from '@/lib/rate-limiter';

describe('防機器人功能測試', () => {
  describe('蜜罐欄位驗證', () => {
    test('空值應該通過驗證', () => {
      const result = validateHoneypot('');
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(0);
    });

    test('undefined 應該通過驗證', () => {
      const result = validateHoneypot(undefined);
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(0);
    });

    test('有值應該被拒絕', () => {
      const result = validateHoneypot('spam');
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(100);
      expect(result.reason).toContain('蜜罐欄位被填寫');
    });

    test('只有空白字元應該被拒絕', () => {
      const result = validateHoneypot('   ');
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(100);
    });
  });

  describe('提交時間驗證', () => {
    test('正常提交時間應該通過', () => {
      const startTime = Date.now() - 10000; // 10 秒前
      const submitTime = Date.now();
      const result = validateSubmissionTime(startTime, submitTime);
      
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBeLessThan(50);
    });

    test('提交過快應該被拒絕', () => {
      const startTime = Date.now() - 1000; // 1 秒前
      const submitTime = Date.now();
      const result = validateSubmissionTime(startTime, submitTime);
      
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(90);
      expect(result.reason).toContain('提交過快');
    });

    test('表單過期應該被拒絕', () => {
      const startTime = Date.now() - (31 * 60 * 1000); // 31 分鐘前
      const submitTime = Date.now();
      const result = validateSubmissionTime(startTime, submitTime);
      
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(30);
      expect(result.reason).toContain('表單過期');
    });
  });

  describe('重複提交驗證', () => {
    test('首次提交應該通過', () => {
      const result = validateDuplicateSubmission('<EMAIL>', null);
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(0);
    });

    test('短時間內重複提交應該被拒絕', () => {
      const lastSubmission = new Date(Date.now() - 2 * 60 * 1000).toISOString(); // 2 分鐘前
      const result = validateDuplicateSubmission('<EMAIL>', lastSubmission);
      
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(80);
      expect(result.reason).toContain('請勿在');
    });

    test('間隔足夠的重複提交應該通過', () => {
      const lastSubmission = new Date(Date.now() - 10 * 60 * 1000).toISOString(); // 10 分鐘前
      const result = validateDuplicateSubmission('<EMAIL>', lastSubmission);
      
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(0);
    });
  });

  describe('User Agent 分析', () => {
    test('正常瀏覽器應該通過', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
      const result = analyzeUserAgent(userAgent);
      
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(0);
    });

    test('可疑 User Agent 應該被拒絕', () => {
      const userAgent = 'python-requests/2.25.1';
      const result = analyzeUserAgent(userAgent);
      
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBe(95);
      expect(result.reason).toContain('可疑的 User Agent');
    });

    test('缺少 User Agent 應該有風險分數', () => {
      const result = analyzeUserAgent(undefined);
      
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBe(20);
      expect(result.reason).toContain('缺少 User Agent');
    });
  });

  describe('綜合驗證', () => {
    test('正常提交應該通過', () => {
      const submissionData = {
        email: '<EMAIL>',
        formStartTime: Date.now() - 10000,
        submissionTime: Date.now(),
        honeypotValue: '',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ip: '***********'
      };

      const result = validateSubmission(submissionData);
      expect(result.isValid).toBe(true);
      expect(result.riskScore).toBeLessThan(50);
    });

    test('可疑提交應該被拒絕', () => {
      const submissionData = {
        email: '<EMAIL>',
        formStartTime: Date.now() - 1000, // 太快
        submissionTime: Date.now(),
        honeypotValue: 'spam', // 蜜罐欄位被填寫
        userAgent: 'bot/1.0',
        ip: '***********'
      };

      const result = validateSubmission(submissionData);
      expect(result.isValid).toBe(false);
      expect(result.riskScore).toBeGreaterThan(50);
    });
  });

  describe('表單 Token', () => {
    test('生成的 token 應該有效', () => {
      const token = generateFormToken();
      expect(token).toBeTruthy();
      expect(token).toMatch(/^\d+_[a-z0-9]+$/);
    });

    test('新生成的 token 應該通過驗證', () => {
      const token = generateFormToken();
      const isValid = validateFormToken(token);
      expect(isValid).toBe(true);
    });

    test('過期的 token 應該被拒絕', () => {
      const oldToken = `${Date.now() - 2000000}_abc123`; // 超過預設過期時間
      const isValid = validateFormToken(oldToken, 1000); // 1 秒過期時間
      expect(isValid).toBe(false);
    });

    test('無效格式的 token 應該被拒絕', () => {
      const invalidToken = 'invalid_token_format';
      const isValid = validateFormToken(invalidToken);
      expect(isValid).toBe(false);
    });
  });
});

describe('速率限制測試', () => {
  // 清理測試環境
  beforeEach(() => {
    // 注意：實際的 requestCache 是模組級別的，這裡只是示例
    // 在真實測試中可能需要 mock 或重置機制
  });

  describe('IP 速率限制', () => {
    test('正常請求應該被允許', () => {
      const result = checkRateLimit('test-ip-1');
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(DEFAULT_RATE_LIMIT_CONFIG.maxRequests);
    });

    test('記錄請求後剩餘次數應該減少', () => {
      // 在非測試環境中測試（暫時修改環境）
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const identifier = 'test-ip-2';
      recordRequest(identifier);

      const result = checkRateLimit(identifier);
      expect(result.remaining).toBeLessThan(DEFAULT_RATE_LIMIT_CONFIG.maxRequests);

      // 恢復環境
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Email 速率限制', () => {
    test('正常 email 請求應該被允許', () => {
      const result = checkEmailRateLimit('<EMAIL>');
      expect(result.allowed).toBe(true);
    });

    test('記錄 email 請求', () => {
      // 在非測試環境中測試（暫時修改環境）
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const email = '<EMAIL>';
      recordEmailRequest(email);

      const result = checkEmailRateLimit(email);
      expect(result.totalHits).toBeGreaterThan(0);

      // 恢復環境
      process.env.NODE_ENV = originalEnv;
    });
  });
});
