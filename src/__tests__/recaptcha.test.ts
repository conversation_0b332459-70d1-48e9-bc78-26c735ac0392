/**
 * reCAPTCHA 功能測試
 */

import { verifyRecaptcha, isRecaptchaEnabled, getRecaptchaConfig } from '@/lib/recaptcha';

// Mock 環境變數
const originalEnv = process.env;

describe('reCAPTCHA 功能測試', () => {
  beforeEach(() => {
    // 重置環境變數
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('預設狀態測試', () => {
    test('未設定環境變數時應該預設為關閉', () => {
      // 清除相關環境變數
      delete process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED;
      delete process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
      delete process.env.RECAPTCHA_SECRET_KEY;

      const enabled = isRecaptchaEnabled();
      expect(enabled).toBe(false);
    });

    test('只設定 ENABLED=true 但沒有金鑰時應該為關閉', () => {
      process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED = 'true';
      delete process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
      delete process.env.RECAPTCHA_SECRET_KEY;

      const enabled = isRecaptchaEnabled();
      expect(enabled).toBe(false);
    });

    test('設定完整配置時應該為啟用', () => {
      process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED = 'true';
      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY = 'test_site_key';
      process.env.RECAPTCHA_SECRET_KEY = 'test_secret_key';

      const enabled = isRecaptchaEnabled();
      expect(enabled).toBe(true);
    });

    test('ENABLED=false 時即使有金鑰也應該為關閉', () => {
      process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED = 'false';
      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY = 'test_site_key';
      process.env.RECAPTCHA_SECRET_KEY = 'test_secret_key';

      const enabled = isRecaptchaEnabled();
      expect(enabled).toBe(false);
    });
  });

  describe('驗證功能測試', () => {
    test('未設定 Secret Key 時應該跳過驗證', async () => {
      delete process.env.RECAPTCHA_SECRET_KEY;

      const result = await verifyRecaptcha('fake_token');
      
      expect(result.isValid).toBe(true);
      expect(result.score).toBe(1.0);
      expect(result.reason).toBe('reCAPTCHA 未啟用');
    });

    test('缺少 token 時應該被拒絕', async () => {
      process.env.RECAPTCHA_SECRET_KEY = 'test_secret_key';

      const result = await verifyRecaptcha('');
      
      expect(result.isValid).toBe(false);
      expect(result.score).toBe(0);
      expect(result.reason).toBe('缺少 reCAPTCHA token');
    });
  });

  describe('配置摘要測試', () => {
    test('未配置時的摘要', () => {
      delete process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED;
      delete process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
      delete process.env.RECAPTCHA_SECRET_KEY;

      const config = getRecaptchaConfig();
      
      expect(config.enabled).toBe(false);
      expect(config.siteKey).toBe('');
      expect(config.hasSecretKey).toBe(false);
    });

    test('完整配置時的摘要', () => {
      process.env.NEXT_PUBLIC_RECAPTCHA_ENABLED = 'true';
      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY = 'test_site_key';
      process.env.RECAPTCHA_SECRET_KEY = 'test_secret_key';

      const config = getRecaptchaConfig();
      
      expect(config.enabled).toBe(true);
      expect(config.siteKey).toBe('test_site_key');
      expect(config.hasSecretKey).toBe(true);
    });
  });
});
