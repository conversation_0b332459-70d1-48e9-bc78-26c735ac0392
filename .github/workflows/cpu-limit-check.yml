name: CPU Limit Check for Cloudflare Pages

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
  # 可選：在自動化測試完成後觸發
  workflow_run:
    workflows: ["自動化測試"]
    types:
      - completed

jobs:
  cpu-limit-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup test environment
      run: |
        # 檢查 .env.example 是否存在
        if [ ! -f .env.example ]; then
          echo "❌ .env.example 檔案不存在"
          exit 1
        fi

        # 複製並設定測試環境
        cp .env.example .env.local
        echo "NODE_ENV=test" >> .env.local
        echo "APP_ENVIRONMENT=sandbox" >> .env.local

        echo "✅ 測試環境設定完成"
        
    - name: Run CPU limit tests
      env:
        # 測試環境的環境變數
        GOOGLE_SHEETS_PRIVATE_KEY: ${{ secrets.TEST_GOOGLE_SHEETS_PRIVATE_KEY }}
        GOOGLE_SHEETS_CLIENT_EMAIL: ${{ secrets.TEST_GOOGLE_SHEETS_CLIENT_EMAIL }}
        PAYUNI_HASH_KEY: ${{ secrets.TEST_PAYUNI_HASH_KEY }}
        PAYUNI_HASH_IV: ${{ secrets.TEST_PAYUNI_HASH_IV }}
        PAYUNI_MERCHANT_ID: ${{ secrets.TEST_PAYUNI_MERCHANT_ID }}
        APP_ENVIRONMENT: sandbox
      run: |
        # 創建初始報告檔案，避免後續步驟找不到檔案
        echo '{"timestamp":"'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'","summary":{"total":0,"passed":0,"warnings":0,"exceeded":0,"passRate":"0.0","avgCPUTime":"0.00","maxCPUTime":"0.00"},"testResults":[],"cpuMeasurements":[],"recommendations":[],"passed":false}' > cpu-test-report.json
        echo '{"timestamp":"'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'","summary":{"totalBenchmarks":0,"passed":0,"warnings":0,"exceeded":0},"benchmarks":[],"cpuReport":{"summary":{"total":0,"passed":0,"warnings":0,"exceeded":0,"passRate":"0.0","avgCPUTime":"0.00","maxCPUTime":"0.00"},"details":[],"recommendations":[]},"recommendations":[]}' > benchmark-report.json

        echo "🧪 執行 CPU 限制測試..."
        node scripts/test-cpu-limits.js || true  # 即使測試失敗也繼續執行
        
    - name: Run key function benchmarks
      env:
        GOOGLE_SHEETS_PRIVATE_KEY: ${{ secrets.TEST_GOOGLE_SHEETS_PRIVATE_KEY }}
        GOOGLE_SHEETS_CLIENT_EMAIL: ${{ secrets.TEST_GOOGLE_SHEETS_CLIENT_EMAIL }}
        PAYUNI_HASH_KEY: ${{ secrets.TEST_PAYUNI_HASH_KEY }}
        PAYUNI_HASH_IV: ${{ secrets.TEST_PAYUNI_HASH_IV }}
        PAYUNI_MERCHANT_ID: ${{ secrets.TEST_PAYUNI_MERCHANT_ID }}
        APP_ENVIRONMENT: sandbox
      run: |
        echo "🎯 執行關鍵功能基準測試..."
        node scripts/benchmark-key-functions.js || true  # 即使測試失敗也繼續執行
        
    - name: Upload test reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cpu-test-reports-${{ github.run_number }}
        path: |
          cpu-test-report.json
          benchmark-report.json
          cpu-performance-report.json
          *-backup-*.json
        retention-days: 30
        
    - name: Generate test summary
      if: always()
      run: |
        echo "## 🧪 CPU 限制測試結果" >> $GITHUB_STEP_SUMMARY
        
        # 檢查是否有測試報告檔案（使用固定檔名）
        if [ -f "cpu-test-report.json" ]; then
          LATEST_REPORT="cpu-test-report.json"
          
          # 提取測試結果
          TOTAL=$(jq -r '.summary.total' "$LATEST_REPORT")
          PASSED=$(jq -r '.summary.passed' "$LATEST_REPORT")
          WARNINGS=$(jq -r '.summary.warnings' "$LATEST_REPORT")
          EXCEEDED=$(jq -r '.summary.exceeded' "$LATEST_REPORT")
          PASS_RATE=$(jq -r '.summary.passRate' "$LATEST_REPORT")
          AVG_CPU=$(jq -r '.summary.avgCPUTime' "$LATEST_REPORT")
          MAX_CPU=$(jq -r '.summary.maxCPUTime' "$LATEST_REPORT")
          OVERALL_PASSED=$(jq -r '.passed' "$LATEST_REPORT")
          
          echo "### 📊 測試統計" >> $GITHUB_STEP_SUMMARY
          echo "- **總測試數**: $TOTAL" >> $GITHUB_STEP_SUMMARY
          echo "- **通過**: $PASSED ✅" >> $GITHUB_STEP_SUMMARY
          echo "- **警告**: $WARNINGS ⚠️" >> $GITHUB_STEP_SUMMARY
          echo "- **超限**: $EXCEEDED ❌" >> $GITHUB_STEP_SUMMARY
          echo "- **通過率**: $PASS_RATE%" >> $GITHUB_STEP_SUMMARY
          echo "- **平均 CPU 時間**: ${AVG_CPU}ms" >> $GITHUB_STEP_SUMMARY
          echo "- **最大 CPU 時間**: ${MAX_CPU}ms" >> $GITHUB_STEP_SUMMARY
          
          if [ "$OVERALL_PASSED" = "true" ]; then
            echo "### ✅ 整體結果: 通過" >> $GITHUB_STEP_SUMMARY
            echo "所有函數都符合 Cloudflare Pages 的 10ms CPU 限制要求。" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ❌ 整體結果: 失敗" >> $GITHUB_STEP_SUMMARY
            echo "有函數超過 10ms CPU 限制，需要優化後才能部署到 Cloudflare Pages。" >> $GITHUB_STEP_SUMMARY
          fi
          
          # 顯示優化建議
          RECOMMENDATIONS=$(jq -r '.recommendations | length' "$LATEST_REPORT")
          if [ "$RECOMMENDATIONS" -gt 0 ]; then
            echo "### 💡 優化建議" >> $GITHUB_STEP_SUMMARY
            jq -r '.recommendations[] | "- **\(.type)**: \(.message)"' "$LATEST_REPORT" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "❌ 未找到測試報告檔案" >> $GITHUB_STEP_SUMMARY
        fi
        
        # 基準測試結果（使用固定檔名）
        if [ -f "benchmark-report.json" ]; then
          BENCHMARK_REPORT="benchmark-report.json"
          
          echo "## 🎯 關鍵功能基準測試" >> $GITHUB_STEP_SUMMARY
          
          TOTAL_BENCHMARKS=$(jq -r '.summary.totalBenchmarks' "$BENCHMARK_REPORT")
          BENCH_PASSED=$(jq -r '.summary.passed' "$BENCHMARK_REPORT")
          BENCH_WARNINGS=$(jq -r '.summary.warnings' "$BENCHMARK_REPORT")
          BENCH_EXCEEDED=$(jq -r '.summary.exceeded' "$BENCHMARK_REPORT")
          
          echo "- **總基準測試**: $TOTAL_BENCHMARKS" >> $GITHUB_STEP_SUMMARY
          echo "- **通過**: $BENCH_PASSED ✅" >> $GITHUB_STEP_SUMMARY
          echo "- **警告**: $BENCH_WARNINGS ⚠️" >> $GITHUB_STEP_SUMMARY
          echo "- **超限**: $BENCH_EXCEEDED ❌" >> $GITHUB_STEP_SUMMARY
          
          # 顯示各功能效能
          echo "### 📊 各功能效能表現" >> $GITHUB_STEP_SUMMARY
          jq -r '.benchmarks[] | select(.error == null) | "- **\(.name)**: 平均 \(.cpuTime.average | tonumber | . * 100 | round / 100)ms, 最大 \(.cpuTime.max | tonumber | . * 100 | round / 100)ms (\(.status))"' "$BENCHMARK_REPORT" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Check CPU limit compliance
      run: |
        # 檢查是否有測試報告（使用固定檔名）
        if [ -f "cpu-test-report.json" ]; then
          PASSED=$(jq -r '.passed' "cpu-test-report.json")

          if [ "$PASSED" = "false" ]; then
            echo "❌ CPU 限制測試失敗 - 有函數超過 10ms 限制"
            echo "請查看測試報告並優化相關函數後再重新提交"
            exit 1
          else
            echo "✅ CPU 限制測試通過 - 所有函數都符合 10ms 限制"
          fi
        else
          echo "❌ 未找到測試報告，測試可能執行失敗"
          exit 1
        fi
        
    - name: Comment PR with results
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // 檢查固定檔名的測試報告
          const reportFile = 'cpu-test-report.json';
          if (!fs.existsSync(reportFile)) {
            console.log('No test report found');
            return;
          }

          const reportData = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
          
          let comment = `## 🧪 CPU 限制測試結果\n\n`;
          comment += `### 📊 測試統計\n`;
          comment += `- **總測試數**: ${reportData.summary.total}\n`;
          comment += `- **通過**: ${reportData.summary.passed} ✅\n`;
          comment += `- **警告**: ${reportData.summary.warnings} ⚠️\n`;
          comment += `- **超限**: ${reportData.summary.exceeded} ❌\n`;
          comment += `- **通過率**: ${reportData.summary.passRate}%\n`;
          comment += `- **平均 CPU 時間**: ${reportData.summary.avgCPUTime}ms\n`;
          comment += `- **最大 CPU 時間**: ${reportData.summary.maxCPUTime}ms\n\n`;
          
          if (reportData.passed) {
            comment += `### ✅ 整體結果: 通過\n`;
            comment += `所有函數都符合 Cloudflare Pages 的 10ms CPU 限制要求，可以安全部署。\n\n`;
          } else {
            comment += `### ❌ 整體結果: 失敗\n`;
            comment += `有函數超過 10ms CPU 限制，需要優化後才能部署到 Cloudflare Pages。\n\n`;
          }
          
          if (reportData.recommendations && reportData.recommendations.length > 0) {
            comment += `### 💡 優化建議\n`;
            reportData.recommendations.forEach(rec => {
              comment += `- **${rec.type}**: ${rec.message}\n`;
              if (rec.suggestions) {
                rec.suggestions.forEach(suggestion => {
                  comment += `  - ${suggestion}\n`;
                });
              }
            });
            comment += `\n`;
          }
          
          comment += `### 📁 測試報告\n`;
          comment += `完整的測試報告已上傳為 Artifact，可在 Actions 頁面下載查看。\n`;
          
          // 發布評論
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
          
    - name: Set deployment gate
      if: always()
      run: |
        # 建立部署閘門檔案（使用固定檔名）
        if [ -f "cpu-test-report.json" ]; then
          PASSED=$(jq -r '.passed' "cpu-test-report.json")

          echo "CPU_LIMIT_CHECK_PASSED=$PASSED" >> $GITHUB_ENV

          if [ "$PASSED" = "true" ]; then
            echo "✅ 設定部署閘門: 通過"
            echo "DEPLOYMENT_APPROVED=true" >> $GITHUB_ENV
          else
            echo "❌ 設定部署閘門: 阻止"
            echo "DEPLOYMENT_APPROVED=false" >> $GITHUB_ENV
          fi
        else
          echo "DEPLOYMENT_APPROVED=false" >> $GITHUB_ENV
        fi

  # 只有在 CPU 限制測試通過時才執行部署檢查
  deploy-check:
    needs: cpu-limit-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && needs.cpu-limit-test.result == 'success'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download test reports
      uses: actions/download-artifact@v4
      with:
        name: cpu-test-reports-${{ github.run_number }}

    - name: Verify deployment readiness
      run: |
        echo "🔍 驗證部署就緒狀態..."

        # 再次檢查測試報告
        if [ -f "cpu-test-report.json" ]; then
          PASSED=$(jq -r '.passed' "cpu-test-report.json")
          EXCEEDED=$(jq -r '.summary.exceeded' "cpu-test-report.json")

          if [ "$PASSED" = "true" ] && [ "$EXCEEDED" = "0" ]; then
            echo "✅ CPU 限制測試通過，可以進行 Cloudflare Pages 部署"
            echo "DEPLOYMENT_READY=true" >> $GITHUB_ENV
          else
            echo "❌ CPU 限制測試未通過，阻止部署"
            echo "DEPLOYMENT_READY=false" >> $GITHUB_ENV
            exit 1
          fi
        else
          echo "❌ 找不到測試報告，阻止部署"
          echo "DEPLOYMENT_READY=false" >> $GITHUB_ENV
          exit 1
        fi

    - name: Notify deployment readiness
      if: env.DEPLOYMENT_READY == 'true'
      run: |
        echo "## 🚀 部署就緒通知" >> $GITHUB_STEP_SUMMARY
        echo "CPU 限制測試已通過，專案可以安全部署到 Cloudflare Pages。" >> $GITHUB_STEP_SUMMARY
        echo "所有函數都符合 10ms CPU 時間限制要求。" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📊 測試摘要" >> $GITHUB_STEP_SUMMARY

        # 顯示測試摘要
        if [ -f "cpu-test-report.json" ]; then
          TOTAL=$(jq -r '.summary.total' "cpu-test-report.json")
          PASSED_COUNT=$(jq -r '.summary.passed' "cpu-test-report.json")
          AVG_CPU=$(jq -r '.summary.avgCPUTime' "cpu-test-report.json")
          MAX_CPU=$(jq -r '.summary.maxCPUTime' "cpu-test-report.json")

          echo "- **總測試數**: $TOTAL" >> $GITHUB_STEP_SUMMARY
          echo "- **通過數量**: $PASSED_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- **平均 CPU 時間**: ${AVG_CPU}ms" >> $GITHUB_STEP_SUMMARY
          echo "- **最大 CPU 時間**: ${MAX_CPU}ms" >> $GITHUB_STEP_SUMMARY
        fi

  # 強制狀態檢查 - 確保 PR 合併前必須通過測試
  status-check:
    needs: cpu-limit-test
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Set status check
      run: |
        if [ "${{ needs.cpu-limit-test.result }}" = "success" ]; then
          echo "✅ CPU 限制測試狀態檢查通過"
          exit 0
        else
          echo "❌ CPU 限制測試狀態檢查失敗"
          echo "必須修復 CPU 限制問題才能合併 PR 或部署"
          exit 1
        fi
