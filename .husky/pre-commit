#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 執行 pre-commit 檢查..."

# 檢查是否有重大變更需要執行 CPU 測試
CHANGED_FILES=$(git diff --cached --name-only)
API_CHANGES=$(echo "$CHANGED_FILES" | grep -E "(src/app/api|lib/|scripts/)" || true)

if [ -n "$API_CHANGES" ]; then
  echo "📡 檢測到 API 或核心檔案變更，執行快速 CPU 檢查..."
  echo "變更的檔案:"
  echo "$API_CHANGES"
  
  # 執行快速 CPU 測試（只測試關鍵功能）
  echo "🧪 執行關鍵功能快速測試..."
  npm run benchmark
  
  if [ $? -ne 0 ]; then
    echo "❌ CPU 限制快速檢查失敗"
    echo "請執行 'npm run cpu-check' 進行完整測試並修復問題"
    exit 1
  fi
  
  echo "✅ CPU 限制快速檢查通過"
else
  echo "ℹ️ 未檢測到 API 變更，跳過 CPU 測試"
fi

# 執行 lint 檢查
echo "🔍 執行 ESLint 檢查..."
npm run lint

if [ $? -ne 0 ]; then
  echo "❌ ESLint 檢查失敗，請修復後再提交"
  exit 1
fi

echo "✅ 所有 pre-commit 檢查通過"
