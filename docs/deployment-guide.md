# Pangea Website 部署指南

> **📖 相關文件**
> - [環境變數配置指南](./environment-variables-guide.md) - 完整的環境變數設定和部署檢查清單
> - [測試指南](./testing-guide.md) - 測試環境配置和執行
> - [開發者工作流程](./developer-workflow.md) - 開發流程和最佳實踐

## 📊 2025年最新平台比較

### Cloudflare Pages vs Vercel 免費方案對比

| 項目 | **Cloudflare Pages (免費)** | **Vercel Hobby (免費)** |
|------|---------------------------|------------------------|
| **🚀 部署限制** |
| 每月部署次數 | 500 次/月 | 100 次/日 (3,000次/月) |
| 建置時間限制 | 20 分鐘 | 45 分鐘 |
| 專案大小限制 | 20,000 檔案，25MB/檔案 | 無明確限制 |
| 並發建置數量 | 1 個 | 1 個 |
| **📈 流量和頻寬** |
| 每月流量限制 | **無限制** | 100 GB/月 |
| 每月請求數限制 | **無限制** (靜態資產) | 1M Edge Requests/月 |
| CDN 節點覆蓋 | 全球 330+ 節點 | 全球節點網路 |
| **⚡ Serverless Functions** |
| 每日/月請求限制 | 100,000 次/日 | 1M 次/月 |
| CPU 時間限制 | 10ms/請求 (免費) | 4 CPU-hrs/月 |
| 記憶體限制 | 128MB | 360 GB-hrs/月 |
| 執行時間限制 | 30 秒 | 10 秒 (可配置到60秒) |
| **🌐 網域和 SSL** |
| 自訂網域數量 | 100 個/專案 | 50 個/專案 |
| SSL 憑證 | 免費 (自動) | 免費 (自動) |
| DNS 要求 | **不強制轉移** | 不強制轉移 |
| **💰 升級成本** |
| 付費方案起價 | $5/月 (Workers Paid) | $20/月 (Pro) |

## 🎯 針對 Pangea Website 的建議

### **推薦：Cloudflare Pages** 

**理由**：
1. **🚀 無限制流量**：對於商業網站成長非常重要
2. **💰 更低的長期成本**：$5/月 vs Vercel 的 $20/月
3. **🔒 更好的安全性**：內建 DDoS 防護和 WAF
4. **📊 適合我們的使用模式**：表單提交和支付 API 調用頻率適中

**需要注意**：
- Function 限制較嚴格：10ms CPU 時間需要優化程式碼
- 部署次數限制：開發階段需注意 500 次/月限制

## 📋 部署步驟指南

### 方案一：Cloudflare Pages 部署

#### 1. 準備工作
```bash
# 確保專案可以正常建置
npm run build

# 檢查環境變數設定
npm run verify:secrets

# 設定環境變數（詳細說明請參考環境變數配置指南）
cp .env.example .env.local
# 編輯 .env.local 並設定必要的環境變數
```

#### 2. 連接 GitHub 到 Cloudflare Pages
1. 登入 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 選擇 **Workers & Pages** > **Create application**
3. 選擇 **Pages** > **Connect to Git**
4. 授權 GitHub 並選擇 `pangea-website` 儲存庫
5. 配置建置設定：
   - **Framework preset**: Next.js
   - **Build command**: `npm run build`
   - **Build output directory**: `.next`
   - **Root directory**: `/` (預設)

#### 3. 環境變數設定
在 Cloudflare Pages 專案設定中：
1. 前往 **Settings** > **Environment variables**
2. 添加必要的環境變數

> **📖 詳細的環境變數清單和設定說明，請參考：**
> [環境變數配置指南](./environment-variables-guide.md#部署平台設定指南)

**必須設定的核心變數：**
- `APP_ENVIRONMENT=production`
- PayUni 憑證（測試和正式環境）
- Google Sheets 憑證和 ID
- URL 設定（NEXT_PUBLIC_BASE_URL, PAYUNI_NOTIFY_URL）
- GTM 設定

#### 4. 自訂網域設定 (保持 Route53 DNS)
1. 在 Cloudflare Pages 中：
   - 前往 **Custom domains** > **Set up a custom domain**
   - 輸入你的網域名稱 (例如：pangea.weaven.co)

2. 在 Amazon Route53 中：
   - 前往你的 Hosted Zone
   - 創建 CNAME 記錄：
     ```
     Name: pangea (或 www)
     Type: CNAME
     Value: your-project.pages.dev
     TTL: 300
     ```

#### 5. 部署和測試
1. 推送程式碼到 GitHub main 分支觸發自動部署
2. 檢查建置日誌確認無錯誤
3. 測試所有功能：
   - 表單提交
   - PayUni 支付整合
   - Google Sheets 寫入

### 方案二：Vercel 部署

#### 1. 連接 GitHub 到 Vercel
1. 登入 [Vercel Dashboard](https://vercel.com/dashboard)
2. 點擊 **New Project**
3. 從 GitHub 匯入 `pangea-website` 儲存庫
4. 配置設定：
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

#### 2. 環境變數設定
在 Vercel 專案設定中：
1. 前往 **Settings** > **Environment Variables**
2. 添加必要的環境變數

> **📖 詳細的環境變數清單請參考：**
> [環境變數配置指南](./environment-variables-guide.md#部署平台設定指南)

#### 3. 自訂網域設定 (保持 Route53 DNS)
1. 在 Vercel 中：
   - 前往 **Settings** > **Domains**
   - 添加你的網域名稱

2. 在 Amazon Route53 中：
   - 創建 CNAME 記錄：
     ```
     Name: pangea
     Type: CNAME  
     Value: cname.vercel-dns.com
     TTL: 300
     ```

## 🔧 DNS 配置詳細說明

### Amazon Route53 保持不變
✅ **兩個平台都支援保持 Route53 DNS 管理**
- 不需要轉移 DNS 到 Cloudflare 或 Vercel
- 只需要添加 CNAME 記錄指向平台提供的目標
- 現有的 MX、TXT 等記錄不受影響

### CNAME 設定步驟
1. 登入 [AWS Route53 Console](https://console.aws.amazon.com/route53/)
2. 選擇你的 Hosted Zone
3. 點擊 **Create record**
4. 設定記錄：
   - **Record name**: `pangea` (或你想要的子網域)
   - **Record type**: `CNAME`
   - **Value**: 
     - Cloudflare: `your-project.pages.dev`
     - Vercel: `cname.vercel-dns.com`
   - **TTL**: `300` (5分鐘)

### 費用影響
- **Route53 費用**：每個 Hosted Zone $0.50/月 + 查詢費用
- **不會增加額外費用**：只是添加 CNAME 記錄
- **現有 AWS 服務**：不受影響，繼續正常運作

## 💰 成本預估和升級時機

### 免費方案使用預估
基於 pangea-website 的流量模式：

**Cloudflare Pages 免費方案足夠條件**：
- 月流量 < 無限制 ✅
- API 請求 < 100K/日 (約3M/月) ✅
- 部署次數 < 500/月 ⚠️ (開發期需注意)

**Vercel Hobby 免費方案足夠條件**：
- 月流量 < 100GB ⚠️ (需監控)
- Edge 請求 < 1M/月 ✅
- Function 調用 < 1M/月 ✅

### 升級時機建議

**Cloudflare Pages 升級到 Workers Paid ($5/月)**：
- API 請求超過 100K/日
- 需要更長的 Function 執行時間 (30秒+)
- 需要更多 CPU 時間

**Vercel 升級到 Pro ($20/月)**：
- 月流量超過 100GB
- 需要團隊協作功能
- 需要更多 Function 資源

## ⚠️ 重要注意事項

### 開發階段建議
1. **使用 Cloudflare Pages**：先在免費方案測試
2. **監控使用量**：特別注意部署次數限制
3. **優化 Function**：確保符合 10ms CPU 限制

### 生產環境準備
1. **設定監控**：追蹤流量和 API 使用量
2. **備份策略**：確保可以快速切換平台
3. **效能測試**：驗證 PayUni 和 Google Sheets 整合

### 遷移風險評估
- **低風險**：兩平台都支援 Next.js
- **中風險**：Function 語法略有差異，需要測試
- **高風險**：Cloudflare 的 10ms CPU 限制較嚴格

## 📞 支援和故障排除

### 常見問題
1. **建置失敗**：檢查 Node.js 版本和依賴
2. **環境變數錯誤**：確認所有必要變數已設定
3. **DNS 解析問題**：等待 TTL 時間或清除 DNS 快取
4. **Function 超時**：優化程式碼或考慮升級方案

### 聯絡支援
- **Cloudflare**: [Community Forum](https://community.cloudflare.com/)
- **Vercel**: [Help Center](https://vercel.com/help)
- **AWS Route53**: [AWS Support](https://aws.amazon.com/support/)

## 🔧 技術配置詳細說明

### Next.js 配置調整

#### Cloudflare Pages 專用配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Cloudflare Pages 需要的配置
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true, // Cloudflare 有自己的圖片優化
  },
  // 如果使用 App Router
  experimental: {
    appDir: true,
  },
}

module.exports = nextConfig
```

#### Vercel 專用配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Vercel 優化配置
  images: {
    domains: ['your-domain.com'],
  },
  // 啟用 Vercel 的圖片優化
  experimental: {
    optimizeCss: true,
  },
}

module.exports = nextConfig
```

### API Routes 優化

#### Cloudflare Pages Functions
```javascript
// pages/api/example.js 或 app/api/example/route.js
export default async function handler(request, env) {
  // Cloudflare 環境變數通過 env 參數訪問
  const apiKey = env.GOOGLE_SHEETS_PRIVATE_KEY;

  // 優化：確保在 10ms CPU 限制內
  try {
    const result = await processRequest(request);
    return new Response(JSON.stringify(result), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
```

#### Vercel Functions
```javascript
// pages/api/example.js 或 app/api/example/route.js
export default async function handler(req, res) {
  // Vercel 環境變數通過 process.env 訪問
  const apiKey = process.env.GOOGLE_SHEETS_PRIVATE_KEY;

  try {
    const result = await processRequest(req);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

### 環境變數安全設定

#### 敏感資料處理
- 所有敏感憑證應存儲在 `.env.local`（本地開發）或部署平台的環境變數中
- 私鑰和密碼不應提交到版本控制系統
- 使用 `.gitignore` 確保敏感檔案不被提交

#### 環境變數驗證
- 使用 `npm run verify:secrets` 檢查環境變數設定
- 部署前確認所有必要的環境變數已設定
- 訪問 `/api/environment-check` 確認部署後的環境配置

> **📖 詳細的環境變數清單和設定說明，請參考：**
> [環境變數配置指南](./environment-variables-guide.md)
```

## 🚀 部署自動化

### GitHub Actions 工作流程
```yaml
# .github/workflows/deploy.yml
name: Deploy to Cloudflare Pages

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build project
        run: npm run build
        env:
          GOOGLE_SHEETS_PRIVATE_KEY: ${{ secrets.GOOGLE_SHEETS_PRIVATE_KEY }}
          GOOGLE_SHEETS_CLIENT_EMAIL: ${{ secrets.GOOGLE_SHEETS_CLIENT_EMAIL }}
          PAYUNI_HASH_KEY: ${{ secrets.PAYUNI_HASH_KEY }}
          PAYUNI_HASH_IV: ${{ secrets.PAYUNI_HASH_IV }}
          PAYUNI_MERCHANT_ID: ${{ secrets.PAYUNI_MERCHANT_ID }}
          PAYUNI_ENVIRONMENT: ${{ secrets.PAYUNI_ENVIRONMENT }}

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: pangea-website
          directory: .next
```

### 部署前檢查清單
- [ ] 運行 `npm run verify:secrets` 檢查環境變數
- [ ] 運行 `npm run build` 確認建置成功
- [ ] 運行 `npm run test` 確認測試通過
- [ ] DNS 記錄已配置
- [ ] SSL 憑證已生效
- [ ] PayUni 測試交易成功

> **📖 完整的部署檢查清單請參考：**
> [環境變數配置指南 - 部署檢查清單](./environment-variables-guide.md#部署檢查清單)
- [ ] Google Sheets 寫入測試成功

## 📊 資源使用量監控分析 (2025年更新)

### Cloudflare Pages 資源限制監控

#### 1. CPU 時間監控 (10ms/請求限制)
```javascript
// lib/cloudflare-monitoring.js
export class CloudflareMonitor {
  static trackCPUTime(functionName, startTime) {
    const duration = Date.now() - startTime;

    // 記錄執行時間
    console.log(`[CF] ${functionName}: ${duration}ms`);

    // 警告：接近 10ms 限制
    if (duration > 8) {
      console.warn(`⚠️ ${functionName} 接近 CPU 限制: ${duration}ms/10ms`);
      // 可以發送警報到監控系統
      this.sendAlert('cpu_limit_warning', { functionName, duration });
    }

    // 錯誤：超過限制
    if (duration > 10) {
      console.error(`❌ ${functionName} 超過 CPU 限制: ${duration}ms`);
    }

    return duration;
  }

  static async sendAlert(type, data) {
    // 整合 Slack、Discord 或其他通知系統
    if (process.env.SLACK_WEBHOOK_URL) {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 Cloudflare 資源警報: ${type}`,
          attachments: [{ color: 'warning', text: JSON.stringify(data) }]
        })
      });
    }
  }
}
```

#### 2. 記憶體使用量監控 (128MB 限制)
```javascript
// lib/memory-monitor.js
export function trackMemoryUsage(functionName) {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage();
    const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
    const totalMB = Math.round(usage.heapTotal / 1024 / 1024);

    console.log(`[記憶體] ${functionName}: ${usedMB}MB/${totalMB}MB (限制: 128MB)`);

    // 警告：使用量超過 80%
    if (usedMB > 102) { // 128MB * 0.8
      console.warn(`⚠️ 記憶體使用量過高: ${usedMB}MB/128MB`);
    }

    return { used: usedMB, total: totalMB, limit: 128 };
  }
  return null;
}
```

#### 3. 執行時間監控 (30秒限制)
```javascript
// lib/execution-monitor.js
export function withExecutionTimeout(fn, timeoutMs = 25000) {
  return async (...args) => {
    const startTime = Date.now();

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`函數執行超時: ${timeoutMs}ms`));
      }, timeoutMs);
    });

    try {
      const result = await Promise.race([fn(...args), timeoutPromise]);
      const duration = Date.now() - startTime;

      console.log(`[執行時間] ${fn.name}: ${duration}ms (限制: 30000ms)`);

      if (duration > 25000) {
        console.warn(`⚠️ 執行時間接近限制: ${duration}ms/30000ms`);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 函數執行失敗 (${duration}ms):`, error.message);
      throw error;
    }
  };
}
```

### Vercel 資源限制監控

#### 1. CPU 時間累積監控 (4 CPU-hrs/月)
```javascript
// lib/vercel-monitoring.js
export class VercelMonitor {
  static async trackMonthlyCPUUsage() {
    // 使用 Vercel Analytics API 獲取使用量
    const response = await fetch(`https://api.vercel.com/v1/analytics/usage`, {
      headers: {
        'Authorization': `Bearer ${process.env.VERCEL_TOKEN}`,
      }
    });

    const data = await response.json();
    const cpuHours = data.cpuHours || 0;
    const limit = 4; // 4 CPU-hrs/月

    console.log(`[Vercel CPU] 本月使用: ${cpuHours}hrs/${limit}hrs`);

    if (cpuHours > limit * 0.8) {
      console.warn(`⚠️ CPU 使用量接近限制: ${cpuHours}hrs/${limit}hrs`);
    }

    return { used: cpuHours, limit };
  }

  static trackFunctionDuration(functionName, startTime) {
    const duration = Date.now() - startTime;
    console.log(`[Vercel] ${functionName}: ${duration}ms`);

    // Vercel 函數執行時間限制為 10-60 秒
    if (duration > 50000) {
      console.warn(`⚠️ 函數執行時間過長: ${duration}ms`);
    }

    return duration;
  }
}
```

### 監控工具建議

#### 1. Cloudflare Analytics API
```javascript
// lib/cloudflare-analytics.js
export async function getCloudflareAnalytics() {
  const response = await fetch(
    `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/analytics/dashboard`,
    {
      headers: {
        'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      }
    }
  );

  const data = await response.json();
  return {
    requests: data.result.totals.requests.all,
    bandwidth: data.result.totals.bandwidth.all,
    errors: data.result.totals.requests.http_status_4xx + data.result.totals.requests.http_status_5xx
  };
}
```

#### 2. 第三方監控整合
```javascript
// lib/monitoring-integrations.js
export class MonitoringIntegrations {
  // Sentry 錯誤追蹤
  static initSentry() {
    if (process.env.SENTRY_DSN) {
      Sentry.init({
        dsn: process.env.SENTRY_DSN,
        environment: process.env.PAYUNI_ENVIRONMENT || 'development',
        beforeSend(event) {
          // 過濾敏感資訊
          if (event.request?.data) {
            delete event.request.data.PAYUNI_HASH_KEY;
            delete event.request.data.GOOGLE_SHEETS_PRIVATE_KEY;
          }
          return event;
        }
      });
    }
  }

  // DataDog 效能監控
  static trackToDataDog(metric, value, tags = {}) {
    if (process.env.DATADOG_API_KEY) {
      fetch('https://api.datadoghq.com/api/v1/series', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'DD-API-KEY': process.env.DATADOG_API_KEY
        },
        body: JSON.stringify({
          series: [{
            metric: `pangea.${metric}`,
            points: [[Math.floor(Date.now() / 1000), value]],
            tags: Object.entries(tags).map(([k, v]) => `${k}:${v}`)
          }]
        })
      });
    }
  }
}
```

### Pangea Website 專案使用量預估

基於網站特性和預期流量的詳細分析：

#### **Cloudflare Pages 使用量評估**
- **每日 API 請求**: 約 500-1,000 次 (表單提交、PayUni 調用、Google Sheets 操作)
- **CPU 時間**: 每次請求約 5-8ms，遠低於 10ms 限制 ✅
- **記憶體使用**: 約 30-50MB，遠低於 128MB 限制 ✅
- **執行時間**: 大部分請求 < 5 秒，遠低於 30 秒限制 ✅
- **部署次數**: 開發期約 20-30 次/月，低於 500 次限制 ✅

#### **Vercel 使用量評估**
- **月流量**: 預估 10-20GB，低於 100GB 限制 ✅
- **Edge 請求**: 約 50K-100K/月，低於 1M 限制 ✅
- **CPU 時間**: 預估 0.5-1 CPU-hrs/月，低於 4 hrs 限制 ✅

#### **風險評估與建議**
1. **Cloudflare Pages 風險**：
   - 🟡 **中風險**: 10ms CPU 限制較嚴格，需要優化程式碼
   - 🟢 **低風險**: 其他限制都有充足餘量

2. **Vercel 風險**：
   - 🟢 **低風險**: 所有限制都有充足餘量
   - 🟡 **中風險**: 流量成長可能觸及 100GB 限制

## 🚀 Cloudflare 部署配置優化 (2025年更新)

### 選擇性部署觸發條件設定

#### 1. 關閉自動部署
在 Cloudflare Pages 專案設定中：
1. 前往 **Settings** > **Builds & deployments**
2. 找到 **Configure Production deployments**
3. **取消勾選** "Enable automatic production branch deployments"
4. 點擊 **Save** 儲存設定

#### 2. 自訂分支部署控制
```yaml
# 在 Cloudflare Pages 設定中配置
Production branch control:
  - Branch: main
  - Automatic deployments: ❌ Disabled

Preview branch control:
  - Mode: Custom branches
  - Include branches: release/*, hotfix/*
  - Exclude branches: feature/*, dev/*, dependabot/*
```

#### 3. 使用 GitHub Actions 控制部署
```yaml
# .github/workflows/deploy-cloudflare.yml
name: Deploy to Cloudflare Pages

on:
  push:
    tags:
      - 'v*'  # 只在版本標籤時部署
  workflow_dispatch:  # 允許手動觸發

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build
        env:
          PAYUNI_ENVIRONMENT: production

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: pangea-website
          directory: .next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
```

## 📊 使用量追蹤

## 🔒 開發環境密碼保護 (2025年更新)

### Cloudflare Pages 密碼保護設定

Cloudflare Pages 支援強大的 Access 功能，提供比 Vercel 更靈活的密碼保護選項。

#### 1. 啟用 Cloudflare Access
1. 登入 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 選擇你的帳戶 > **Zero Trust** > **Access** > **Applications**
3. 點擊 **Add an application** > **Self-hosted**

#### 2. 配置應用程式設定
```yaml
Application Configuration:
  Application name: Pangea Website - Development
  Session Duration: 24 hours
  Application domain:
    - Type: Subdomain
    - Domain: dev.pangea.weaven.co (或你的預覽網域)
```

#### 3. 設定認證方式
```yaml
Identity providers:
  - One-time PIN (OTP) via Email
  - GitHub OAuth (推薦給開發團隊)
  - Google OAuth
  - 自訂密碼 (PIN)

Access policies:
  - Policy name: Development Team Access
  - Action: Allow
  - Rules:
    - Email: 包含 @weaven.co
    - 或 GitHub 組織成員
```

#### 4. 進階保護設定
```javascript
// 在應用程式中添加額外的保護層
// middleware.js (Next.js 13+)
import { NextResponse } from 'next/server';

export function middleware(request) {
  // 檢查是否為開發環境
  if (process.env.PAYUNI_ENVIRONMENT === 'sandbox') {
    const authHeader = request.headers.get('cf-access-authenticated-user-email');

    // 如果沒有 Cloudflare Access 認證
    if (!authHeader) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // 檢查是否為授權的電子郵件
    const allowedEmails = process.env.ALLOWED_EMAILS?.split(',') || [];
    if (!allowedEmails.includes(authHeader)) {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

### Vercel 密碼保護設定

#### 1. 使用 Vercel 內建密碼保護
```yaml
Vercel Dashboard 設定:
  1. 前往專案 Settings > Deployment Protection
  2. 啟用 "Password Protection"
  3. 設定密碼
  4. 選擇保護範圍:
     - All deployments (所有部署)
     - Preview deployments only (僅預覽部署)
```

#### 2. 自訂密碼保護中介軟體
```javascript
// middleware.js
import { NextResponse } from 'next/server';

export function middleware(request) {
  // 僅在預覽環境啟用密碼保護
  if (process.env.VERCEL_ENV === 'preview') {
    const basicAuth = request.headers.get('authorization');

    if (!basicAuth) {
      return new NextResponse('Authentication required', {
        status: 401,
        headers: {
          'WWW-Authenticate': 'Basic realm="Secure Area"',
        },
      });
    }

    const auth = basicAuth.split(' ')[1];
    const [user, password] = Buffer.from(auth, 'base64').toString().split(':');

    if (user !== 'admin' || password !== process.env.PREVIEW_PASSWORD) {
      return new NextResponse('Invalid credentials', { status: 401 });
    }
  }

  return NextResponse.next();
}
```

### 比較總結

| 功能 | **Cloudflare Pages** | **Vercel** |
|------|---------------------|------------|
| **內建密碼保護** | ✅ Cloudflare Access | ✅ Deployment Protection |
| **認證方式** | 多種 (Email, OAuth, PIN) | 密碼 |
| **細粒度控制** | ✅ 基於規則的存取控制 | ⚠️ 基本密碼保護 |
| **團隊管理** | ✅ 支援組織和群組 | ⚠️ 共享密碼 |
| **成本** | 免費 (基本功能) | Pro 方案 $150/月 |

**建議**: Cloudflare Pages 的 Access 功能更適合團隊協作和安全需求。

## ⚡ 平台效能比較 (2025年最新數據)

### 全球 CDN 效能測試結果

基於 2025年1月的第三方效能測試數據 (來源: Bejamas.com)：

#### 靜態內容載入速度 (TTFB)
| 地區 | **Cloudflare Pages** | **Vercel** | **優勢** |
|------|---------------------|------------|----------|
| 🇺🇸 美國西部 | 72ms | 26ms | Vercel 快 64% |
| 🇺🇸 美國東部 | 60ms | 27ms | Vercel 快 55% |
| 🇨🇦 加拿大 | 67ms | 69ms | 相近 |
| 🇧🇷 巴西 | 68ms | 29ms | Vercel 快 57% |
| 🇩🇪 德國 | 169ms | 30ms | Vercel 快 82% |
| 🇬🇧 英國 | 80ms | 23ms | Vercel 快 71% |
| 🇸🇬 新加坡 | 72ms | 27ms | Vercel 快 63% |
| 🇯🇵 日本 | 104ms | 30ms | Vercel 快 71% |
| 🇦🇺 澳洲 | 60ms | 29ms | Vercel 快 52% |

#### Edge Functions 效能比較
| 地區 | **Cloudflare Pages** | **Vercel Edge** | **優勢** |
|------|---------------------|-----------------|----------|
| 🇺🇸 美國西部 | 48ms | 1342ms | Cloudflare 快 96% |
| 🇺🇸 美國東部 | 46ms | 170ms | Cloudflare 快 73% |
| 🇩🇪 德國 | 45ms | 369ms | Cloudflare 快 88% |
| 🇸🇬 新加坡 | 52ms | 197ms | Cloudflare 快 74% |
| 🇯🇵 日本 | 60ms | 1336ms | Cloudflare 快 96% |

### 針對台灣用戶的效能分析

#### 1. 地理位置優勢
- **Cloudflare**: 在台北、高雄都有數據中心，延遲通常 < 20ms
- **Vercel**: 主要依賴新加坡和日本節點，延遲約 30-50ms

#### 2. 實際測試建議
```javascript
// lib/performance-test.js
export async function testPlatformPerformance() {
  const testUrls = {
    cloudflare: 'https://your-site.pages.dev',
    vercel: 'https://your-site.vercel.app'
  };

  const results = {};

  for (const [platform, url] of Object.entries(testUrls)) {
    const startTime = performance.now();

    try {
      const response = await fetch(url);
      const endTime = performance.now();

      results[platform] = {
        ttfb: endTime - startTime,
        status: response.status,
        size: response.headers.get('content-length')
      };
    } catch (error) {
      results[platform] = { error: error.message };
    }
  }

  return results;
}
```

### 效能優化建議

#### 1. Cloudflare Pages 優化
```javascript
// 針對 10ms CPU 限制的優化
export async function optimizedAPIHandler(request) {
  const startTime = Date.now();

  try {
    // 使用快取減少計算
    const cacheKey = `api_${request.url}`;
    const cached = await caches.default.match(cacheKey);

    if (cached) {
      console.log(`Cache hit: ${Date.now() - startTime}ms`);
      return cached;
    }

    // 最小化資料庫查詢
    const result = await processRequestOptimized(request);

    // 快取結果
    const response = new Response(JSON.stringify(result), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300'
      }
    });

    await caches.default.put(cacheKey, response.clone());

    console.log(`Total time: ${Date.now() - startTime}ms`);
    return response;

  } catch (error) {
    console.error(`Error after ${Date.now() - startTime}ms:`, error);
    throw error;
  }
}
```

#### 2. Vercel 優化
```javascript
// 利用 Vercel 的較寬鬆限制
export default async function handler(req, res) {
  // 可以進行更複雜的處理
  const result = await complexDataProcessing(req.body);

  // 支援較大的回應
  res.status(200).json({
    data: result,
    timestamp: new Date().toISOString(),
    processingTime: `${Date.now() - startTime}ms`
  });
}
```

### 效能測試工具推薦

1. **WebPageTest**: 全球多點效能測試
2. **GTmetrix**: 詳細的效能分析報告
3. **Lighthouse**: Google 的效能評估工具
4. **Pingdom**: 持續監控和警報

### 自動使用量報告

### 成本警報設定
```javascript
// lib/cost-alerts.js
export async function checkUsageLimits() {
  const usage = await getCurrentUsage();

  const limits = {
    cloudflare: {
      dailyRequests: 100000,
      monthlyDeployments: 500
    },
    vercel: {
      monthlyBandwidth: 100 * 1024 * 1024 * 1024, // 100GB
      monthlyRequests: 1000000
    }
  };

  // 發送警報如果接近限制
  if (usage.requests > limits.cloudflare.dailyRequests * 0.8) {
    console.warn('Approaching daily request limit');
    // 發送通知
  }
}
```

## 📋 2025年部署決策指南

### 選擇建議矩陣

| 考量因素 | **Cloudflare Pages** | **Vercel** | **建議** |
|----------|---------------------|------------|----------|
| **預期月流量** | < 無限制 | < 100GB | 高流量選 Cloudflare |
| **開發團隊規模** | 1-5人 | 1-10人 | 小團隊兩者皆可 |
| **預算考量** | $0-5/月 | $0-20/月 | 成本敏感選 Cloudflare |
| **技術複雜度** | 中等 | 簡單 | 快速上線選 Vercel |
| **效能要求** | 台灣用戶優先 | 全球均衡 | 看目標市場 |
| **安全需求** | 高 | 中等 | 企業級選 Cloudflare |

### 階段性部署策略

#### 第一階段：測試驗證 (1-2週)
```bash
# 1. 在兩個平台都建立測試環境
git checkout -b test-deployment

# 2. Cloudflare Pages 測試
npm run build
npx wrangler pages deploy .next --project-name pangea-test

# 3. Vercel 測試
npx vercel --prod --name pangea-test

# 4. 效能和功能測試
npm run test:e2e
```

#### 第二階段：效能比較 (1週)
```javascript
// scripts/performance-comparison.js
const testResults = await Promise.all([
  testPlatform('cloudflare', 'https://pangea-test.pages.dev'),
  testPlatform('vercel', 'https://pangea-test.vercel.app')
]);

console.table(testResults);
```

#### 第三階段：正式部署決策
基於測試結果選擇最適合的平台進行正式部署。

### 遷移風險評估

#### 低風險項目 ✅
- 靜態資源部署
- 環境變數配置
- DNS 設定
- SSL 憑證

#### 中風險項目 ⚠️
- API Routes 語法差異
- 函數執行環境差異
- 建置配置調整

#### 高風險項目 ❌
- Cloudflare 10ms CPU 限制
- 平台特定功能依賴
- 第三方整合差異

### 監控和維護計畫

#### 每日監控項目
- [ ] API 回應時間
- [ ] 錯誤率統計
- [ ] 資源使用量
- [ ] 使用者體驗指標

#### 每週檢查項目
- [ ] 流量趨勢分析
- [ ] 成本使用評估
- [ ] 效能基準測試
- [ ] 安全性掃描

#### 每月評估項目
- [ ] 平台限制接近度
- [ ] 升級需求評估
- [ ] 競品平台比較
- [ ] 成本效益分析

## 🎯 最終建議

### 2025年推薦策略

**對於 Pangea Website 專案**，建議採用以下策略：

1. **主要部署平台**：**Cloudflare Pages**
   - 理由：無限制流量、更低成本、更好的台灣用戶體驗
   - 風險緩解：優化程式碼以符合 10ms CPU 限制

2. **備用部署平台**：**Vercel**
   - 用途：開發測試、緊急備援
   - 優勢：更寬鬆的函數限制、更好的開發體驗

3. **混合策略**：
   - 靜態內容：Cloudflare Pages
   - 複雜 API：Vercel Functions (如需要)
   - 監控：兩平台並行監控
