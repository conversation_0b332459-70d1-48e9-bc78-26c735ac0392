# 開發者工作流程指南

## 🔄 自動化 CPU 限制檢查流程

### 📋 概述

本專案已設定完整的自動化流程，確保所有程式碼變更都符合 Cloudflare Pages 的 10ms CPU 限制要求。

## 🚀 日常開發流程

### 1. 本地開發

```bash
# 正常開發，無需手動執行 CPU 測試
git add .
git commit -m "feat: 新增功能"

# pre-commit hook 會自動執行：
# - 檢測 API 變更
# - 執行快速 CPU 檢查（如有需要）
# - 執行 ESLint 檢查
```

### 2. 何時需要手動執行測試

**🔴 必須執行完整測試的情況**：
- 新增或修改 API 路由
- 修改核心業務邏輯（PayUni、Google Sheets）
- 修改效能相關的程式碼
- 重大重構

**執行命令**：
```bash
# 完整 CPU 限制檢查
npm run cpu-check

# 只執行關鍵功能測試（較快）
npm run cpu-check:quick

# 預部署檢查（包含所有測試）
npm run pre-deploy
```

### 3. 提交和推送

```bash
# 推送到遠端
git push origin feature-branch

# GitHub Actions 會自動執行：
# - 完整 CPU 限制測試
# - 關鍵功能基準測試
# - 生成測試報告
# - 設定部署閘門
```

## 🔍 自動化檢查說明

### Pre-commit Hook

**觸發條件**：
- 檢測到 `src/app/api/`、`lib/`、`scripts/` 目錄下的檔案變更

**執行內容**：
1. 🧪 執行關鍵功能快速測試
2. 🔍 執行 ESLint 檢查
3. ✅ 通過才允許提交

**跳過方法**（緊急情況）：
```bash
git commit -m "fix: 緊急修復" --no-verify
```

### GitHub Actions CI/CD

**觸發條件**：
- Push 到 `main` 或 `develop` 分支
- 建立 Pull Request 到 `main` 分支
- 手動觸發

**執行流程**：
1. 🧪 CPU 限制測試
2. 🎯 關鍵功能基準測試
3. 📊 生成測試報告
4. 💬 在 PR 中評論結果
5. 🚪 設定部署閘門

## 📊 測試報告管理

### 固定檔名系統

**主要報告檔案**：
- `cpu-test-report.json` - CPU 限制測試報告
- `benchmark-report.json` - 基準測試報告
- `cpu-performance-report.json` - 效能監控報告

**備份系統**：
- 自動備份最近 5 份歷史報告
- 格式：`cpu-test-report-backup-[timestamp].json`
- 自動清理舊備份檔案

### 查看測試結果

```bash
# 查看最新測試結果
cat cpu-test-report.json | jq '.summary'

# 查看基準測試結果
cat benchmark-report.json | jq '.summary'

# 查看優化建議
cat cpu-test-report.json | jq '.recommendations'
```

## 🚨 問題處理

### CPU 限制測試失敗

**1. 查看測試報告**：
```bash
# 查看失敗的函數
cat cpu-test-report.json | jq '.cpuMeasurements[] | select(.status == "EXCEEDED")'

# 查看優化建議
cat cpu-test-report.json | jq '.recommendations'
```

**2. 應用優化策略**：
```javascript
// 使用優化工具
import { optimizationToolkit } from '../lib/optimization-strategies.js';

export const POST = optimizationToolkit.optimizeFunction(async (request) => {
  // 你的 API 邏輯
}, { name: 'MyAPI', timeout: 8000 });
```

**3. 重新測試**：
```bash
npm run cpu-check
```

### CI/CD 測試失敗

**1. 檢查 GitHub Actions 日誌**
**2. 查看 PR 評論中的測試結果**
**3. 本地重現問題**：
```bash
# 確保環境一致
npm ci
npm run cpu-check
```

### Pre-commit Hook 失敗

**1. 查看錯誤訊息**
**2. 修復問題後重新提交**：
```bash
# 修復問題
npm run cpu-check

# 重新提交
git add .
git commit -m "fix: 修復 CPU 限制問題"
```

## 🔧 自訂配置

### 調整 CPU 限制閾值

```javascript
// lib/cpu-time-monitor.js
const cpuMonitor = new CPUTimeMonitor({
  cpuLimit: 10,        // CPU 限制 (ms)
  warningThreshold: 8  // 警告閾值 (ms)
});
```

### 調整備份檔案數量

```javascript
// scripts/test-cpu-limits.js
await this.cleanupOldBackups('.', 'cpu-test-report', 5); // 保留 5 份備份
```

### 自訂 Pre-commit 檢查

編輯 `.husky/pre-commit` 檔案：
```bash
# 添加自訂檢查
echo "🔍 執行自訂檢查..."
# 你的自訂邏輯
```

## 📚 最佳實踐

### 1. 開發習慣

- ✅ 小步提交，頻繁測試
- ✅ 重大變更前先執行完整測試
- ✅ 關注 pre-commit hook 的輸出
- ✅ 定期查看測試報告趨勢

### 2. 效能優化

- ✅ 使用提供的優化策略
- ✅ 避免在 API 中進行重計算
- ✅ 善用快取機制
- ✅ 監控記憶體使用量

### 3. 團隊協作

- ✅ PR 前確保測試通過
- ✅ 分享優化經驗
- ✅ 及時修復 CI/CD 失敗
- ✅ 保持測試環境一致

## 🎯 故障排除

### 常見問題

**Q: Pre-commit hook 執行太慢？**
A: 可以調整檢查條件，只在重要變更時執行

**Q: GitHub Actions 測試超時？**
A: 檢查是否有無限迴圈或過度計算

**Q: 測試報告檔案過多？**
A: 備份清理機制會自動處理，也可手動清理

**Q: 如何暫時跳過檢查？**
A: 使用 `--no-verify` 標誌，但不建議經常使用

## 📞 支援

如有問題，請：
1. 查看 [CPU 限制測試指南](cpu-limit-testing-guide.md)
2. 檢查 GitHub Actions 日誌
3. 聯繫開發團隊

---

**記住**：自動化是為了幫助開發，不是阻礙。如果遇到問題，優先修復而不是跳過檢查。
