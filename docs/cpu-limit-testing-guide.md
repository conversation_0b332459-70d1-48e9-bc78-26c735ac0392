# Cloudflare Pages CPU 限制測試指南

## 📋 概述

本指南提供完整的解決方案來測試和優化 Pangea Website 專案，確保所有 API 函數都符合 Cloudflare Pages 的 10ms CPU 時間限制要求。

## 🛠 工具組件

### 1. CPU 時間監控工具 (`lib/cpu-time-monitor.js`)
- **功能**: 精確測量每個 API 函數的 CPU 執行時間
- **特色**: 模擬 Cloudflare 的 10ms 限制，提供詳細的效能分析
- **輸出**: JSON 格式的測試報告，包含優化建議

### 2. 自動化測試腳本 (`scripts/test-cpu-limits.js`)
- **功能**: 批量測試所有 API 端點
- **特色**: 自動發現 API 路由，標記超過限制的函數
- **輸出**: 完整的測試報告和通過/失敗狀態

### 3. 關鍵功能基準測試 (`scripts/benchmark-key-functions.js`)
- **功能**: 針對核心業務功能進行深度效能測試
- **測試範圍**: 表單提交、PayUni 支付、Google Sheets 操作
- **輸出**: 詳細的效能統計和優化建議

### 4. 優化策略庫 (`lib/optimization-strategies.js`)
- **功能**: 提供具體的程式碼優化實作
- **包含**: 快取管理、記憶體優化、非同步處理優化
- **使用**: 可直接整合到現有 API 路由中

### 5. CI/CD 整合 (`.github/workflows/cpu-limit-check.yml`)
- **功能**: GitHub Actions 自動化測試
- **觸發**: 每次 push 和 PR 都會執行 CPU 限制檢查
- **閘門**: 只有通過測試才允許部署

## 🚀 快速開始

### 1. 安裝和設定

```bash
# 安裝依賴（如果尚未安裝）
npm install

# 設定環境變數
cp .env.example .env.local

# 添加測試環境變數
echo "NODE_ENV=test" >> .env.local
echo "PAYUNI_ENVIRONMENT=sandbox" >> .env.local
```

### 2. 執行基本測試

```bash
# 執行 CPU 限制測試
npm run test:cpu

# 執行關鍵功能基準測試
npm run benchmark

# 執行完整的 CPU 檢查
npm run cpu-check
```

### 3. 查看測試報告

測試完成後會生成以下報告檔案：
- `cpu-test-report-[timestamp].json` - CPU 限制測試報告
- `benchmark-report-[timestamp].json` - 基準測試報告
- `cpu-performance-report.json` - 效能監控報告

## 📊 測試報告解讀

### CPU 限制測試報告結構

```json
{
  "timestamp": "2025-01-XX...",
  "summary": {
    "total": 15,           // 總測試數
    "passed": 12,          // 通過數量
    "warnings": 2,         // 警告數量
    "exceeded": 1,         // 超限數量
    "passRate": "80.0",    // 通過率
    "avgCPUTime": "6.5",   // 平均 CPU 時間
    "maxCPUTime": "12.3"   // 最大 CPU 時間
  },
  "passed": false,         // 整體是否通過
  "recommendations": [     // 優化建議
    {
      "type": "CRITICAL",
      "message": "1 個函數超過 CPU 限制，必須優化",
      "functions": ["PayUniCreateOrder"],
      "suggestions": [
        "減少同步計算操作",
        "使用快取減少重複計算"
      ]
    }
  ]
}
```

### 狀態說明

- **✅ OK**: CPU 時間 ≤ 8ms，安全範圍
- **⚠️ WARNING**: CPU 時間 8-10ms，接近限制
- **❌ EXCEEDED**: CPU 時間 > 10ms，超過限制

## 🔧 在現有 API 中整合監控

### 方法 1: 使用裝飾器（推薦）

```javascript
import { cpuMonitor } from '../lib/cpu-time-monitor.js';

export async function POST(request) {
  return cpuMonitor.measureCPUTime('ContactForm', async () => {
    // 你的 API 邏輯
    const formData = await request.json();
    
    // 處理表單資料
    const result = processFormData(formData);
    
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  });
}
```

### 方法 2: 使用優化中介軟體

```javascript
import { withOptimizations } from '../examples/optimized-api-routes.js';

export const POST = withOptimizations(async (request) => {
  // 你的 API 邏輯
  return new Response('OK');
}, { 
  name: 'MyAPI', 
  timeout: 8000 
});
```

### 方法 3: 手動包裝

```javascript
import { cpuMonitor } from '../lib/cpu-time-monitor.js';

const originalHandler = async (request) => {
  // 原始 API 邏輯
};

export const POST = cpuMonitor.wrapAppRouterAPI('MyAPI', originalHandler);
```

## 🎯 優化策略指南

### 1. 表單驗證優化

**問題**: 複雜的正則表達式和字串操作耗時
**解決方案**:
```javascript
import { OptimizedFormValidator } from '../lib/optimization-strategies.js';

const validator = new OptimizedFormValidator();

// 使用預編譯的正則表達式
const validation = validator.validateForm(formData);
```

### 2. PayUni 加密優化

**問題**: 重複的加密計算和記憶體分配
**解決方案**:
```javascript
import { OptimizedPayUniCrypto } from '../lib/optimization-strategies.js';

const crypto = new OptimizedPayUniCrypto();

// 使用快取和優化的加密
const order = await crypto.createOrderFast(orderData);
```

### 3. Google Sheets 操作優化

**問題**: 頻繁的 API 調用和大量資料處理
**解決方案**:
```javascript
import { OptimizedSheetsOperations } from '../lib/optimization-strategies.js';

const sheets = new OptimizedSheetsOperations();

// 批次處理和快取
await sheets.writeDataOptimized(data);
```

### 4. 記憶體管理

**問題**: 記憶體使用量過高影響效能
**解決方案**:
```javascript
import { MemoryOptimizer } from '../lib/optimization-strategies.js';

const memoryOptimizer = new MemoryOptimizer();

// 處理完成後清理記憶體
memoryOptimizer.cleanupLargeObjects(largeData);
memoryOptimizer.forceGC();
```

## 🔄 CI/CD 整合

### GitHub Actions 自動檢查

每次 push 或 PR 都會自動執行：

1. **CPU 限制測試**: 檢查所有 API 函數
2. **基準測試**: 測試關鍵功能效能
3. **報告生成**: 自動生成測試報告
4. **PR 評論**: 在 PR 中顯示測試結果
5. **部署閘門**: 只有通過測試才允許部署

### 本地預部署檢查

```bash
# 執行完整的預部署檢查
npm run pre-deploy

# 這會依序執行：
# 1. ESLint 檢查
# 2. 單元測試
# 3. CPU 限制測試
# 4. 基準測試
```

## 📈 效能監控和持續優化

### 1. 設定效能基準

```bash
# 建立效能基準
npm run benchmark > baseline-performance.txt

# 定期比較效能變化
npm run benchmark | diff baseline-performance.txt -
```

### 2. 監控趨勢

- 每週執行基準測試
- 追蹤平均 CPU 時間變化
- 識別效能退化的函數

### 3. 優化循環

1. **識別**: 找出超過限制的函數
2. **分析**: 使用測試報告分析瓶頸
3. **優化**: 應用相應的優化策略
4. **驗證**: 重新測試確認改善
5. **部署**: 通過測試後部署

## 🚨 常見問題和解決方案

### Q1: 測試顯示某個函數超過 10ms 限制怎麼辦？

**A**: 
1. 查看測試報告中的優化建議
2. 使用 `lib/optimization-strategies.js` 中的相應工具
3. 重新測試驗證改善效果

### Q2: 如何處理第三方 API 調用的延遲？

**A**:
1. 使用快取減少重複調用
2. 實作超時機制
3. 考慮非同步處理或背景任務

### Q3: 記憶體使用量過高怎麼辦？

**A**:
1. 使用 `MemoryOptimizer` 監控記憶體
2. 及時清理大型物件
3. 強制執行垃圾回收

### Q4: CI/CD 測試失敗但本地測試通過？

**A**:
1. 檢查環境變數設定
2. 確認測試資料的一致性
3. 查看 GitHub Actions 日誌

## 📚 進階使用

### 自訂測試場景

```javascript
// 建立自訂測試
import { CPULimitTester } from '../scripts/test-cpu-limits.js';

const tester = new CPULimitTester();

// 添加自訂測試資料
tester.testData.customScenario = {
  // 你的測試資料
};

// 執行測試
const report = await tester.runAllTests();
```

### 整合到現有監控系統

```javascript
import { cpuMonitor } from '../lib/cpu-time-monitor.js';

// 設定自訂警報
cpuMonitor.onExceeded = (measurement) => {
  // 發送到你的監控系統
  sendToMonitoring(measurement);
};
```

## 🎉 總結

通過使用這套完整的 CPU 限制測試解決方案，你可以：

1. **提前發現問題**: 在部署前識別超過限制的函數
2. **自動化測試**: CI/CD 整合確保每次變更都經過檢查
3. **具體優化指導**: 獲得針對性的優化建議
4. **持續監控**: 追蹤效能趨勢和改善效果
5. **安全部署**: 確保所有函數都符合 Cloudflare Pages 要求

開始使用：`npm run cpu-check` 🚀
