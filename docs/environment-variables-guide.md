# 環境變數配置指南

> **📖 相關文件**
> - [部署指南](./deployment-guide.md) - 詳細的部署平台比較和步驟
> - [測試指南](./testing-guide.md) - 測試環境配置和執行

## 📋 環境配置狀態（已更新 - 2025-07-15）

### ✅ 統一環境配置系統

#### 1. 環境控制變數
```bash
# 統一環境控制（取代舊的 PAYUNI_ENVIRONMENT）
APP_ENVIRONMENT=sandbox  # 或 production
NODE_ENV=development     # 或 test, production

# APP_ENVIRONMENT 控制業務邏輯環境：
# - PayUni 支付系統
# - Google Sheets 資料來源
# - Meta Pixel 追蹤
# - GTM 設定

# NODE_ENV 控制框架行為：
# - Next.js 最佳化模式
# - 錯誤訊息詳細程度
# - 快取日誌顯示
# - 開發者工具啟用
```

#### 2. PayUni 付款系統
```bash
# 測試環境憑證
PAYUNI_SANDBOX_MER_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=********************************
PAYUNI_SANDBOX_HASH_IV=2fjTDwg7OCFid91U

# 正式環境憑證
PAYUNI_PRODUCTION_MER_ID=SHOP6220811892463388
PAYUNI_PRODUCTION_HASH_KEY=bWoacqmMTH1o14jJHSWsgBIy3u5KKqbI
PAYUNI_PRODUCTION_HASH_IV=DeN566gVaWyrjvbh
```

#### 3. Google Sheets 整合（已更新為環境區分）
```bash
# 服務帳戶憑證（所有環境共用）
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# 測試環境 Sheet ID
GOOGLE_SANDBOX_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8
GOOGLE_SANDBOX_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY
GOOGLE_SANDBOX_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg

# 正式環境 Sheet ID（目前與測試環境相同）
GOOGLE_PRODUCTION_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8
GOOGLE_PRODUCTION_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY
GOOGLE_PRODUCTION_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg

# 向後相容性（系統會自動回退到這些變數）
GOOGLE_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8
GOOGLE_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY
GOOGLE_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg
```

#### 4. Meta Pixel 環境配置
```bash
# 測試環境 Meta Pixel 設定
META_SANDBOX_PIXEL_ID=************5156
META_SANDBOX_ACCESS_TOKEN=EAAMJz9Wz9CkBPIQZB6HShYqZCoVwLGQMZA0EEERfrkyUkX4O56qhGviDvIHOixJbDJC9ZBjUnNmUeimGHCGDZCnvYDqcClQsnMCw27THOlQDVki2ayWKyBKQ04GYoCnf98xJBzJTRuQOPNZBmL6ZAmzyMXNhEuLNFJi1ZBzlnhdjAn9KefjZAEMZC2nsLFDiKSMfMx3AZDZD
META_SANDBOX_TEST_EVENT_CODE=TEST15370

# 正式環境 Meta Pixel 設定（目前與測試環境相同）
META_PRODUCTION_PIXEL_ID=************5156
META_PRODUCTION_ACCESS_TOKEN=EAAMJz9Wz9CkBPIQZB6HShYqZCoVwLGQMZA0EEERfrkyUkX4O56qhGviDvIHOixJbDJC9ZBjUnNmUeimGHCGDZCnvYDqcClQsnMCw27THOlQDVki2ayWKyBKQ04GYoCnf98xJBzJTRuQOPNZBmL6ZAmzyMXNhEuLNFJi1ZBzlnhdjAn9KefjZAEMZC2nsLFDiKSMfMx3AZDZD
META_PRODUCTION_TEST_EVENT_CODE=TEST15370
```

#### 5. GTM 環境配置
```bash
# 測試環境 GTM 設定
NEXT_PUBLIC_GTM_SANDBOX_ID=GTM-TEST123
NEXT_PUBLIC_GTM_SANDBOX_PROXY_DOMAIN=gtm.pangea.weaven.co

# 正式環境 GTM 設定（目前與測試環境相同）
NEXT_PUBLIC_GTM_PRODUCTION_ID=GTM-TEST123
NEXT_PUBLIC_GTM_PRODUCTION_PROXY_DOMAIN=gtm.pangea.weaven.co
```

### 🔄 快取配置說明

#### 快取 TTL 設定
```bash
# 各區塊專用 TTL 設定（程式碼中固定）
部落格快取：5 分鐘（固定）
手錶快取：5 分鐘（固定）
FAQ 快取：10 分鐘（固定）
通用快取：5 分鐘（可透過 CACHE_TTL 調整）
```

#### 快取環境變數（可選）
```bash
# 這些環境變數有合理的預設值，通常不需要設定
CACHE_ENABLED=true        # 預設啟用
CACHE_TTL=300000          # 預設 5 分鐘（僅影響通用快取）
CACHE_MAX_SIZE=50         # 預設 50 個項目
CACHE_LOGGING=false       # 生產環境建議關閉（開發環境自動啟用）
```

### 🧪 NODE_ENV 測試情境

#### 何時需要手動調整 NODE_ENV？

**1. 測試生產環境行為（本地）**
```bash
# 測試生產環境的錯誤處理和日誌
NODE_ENV=production npm run dev
```

**2. 測試快取日誌開關**
```bash
# 開發環境 - 顯示快取日誌
NODE_ENV=development npm run dev

# 生產環境 - 隱藏快取日誌
NODE_ENV=production npm run dev
```

**3. 測試錯誤訊息差異**
```bash
# 開發環境 - 詳細錯誤訊息
NODE_ENV=development npm run dev

# 生產環境 - 簡化錯誤訊息
NODE_ENV=production npm run dev
```

**4. 部署前測試**
```bash
# 模擬生產環境但保持開發伺服器
NODE_ENV=production npm run dev
```

## 🚀 部署環境變數配置

### **🔴 必須設定的環境變數**

#### 1. 環境控制
```bash
APP_ENVIRONMENT=production    # 必須設為 production
NODE_ENV=production          # 由部署平台自動設定
```

#### 2. PayUni 支付系統憑證
```bash
# 測試環境憑證（開發用）
PAYUNI_SANDBOX_MER_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=********************************
PAYUNI_SANDBOX_HASH_IV=2fjTDwg7OCFid91U

# 正式環境憑證（生產用）
PAYUNI_PRODUCTION_MER_ID=SHOP6220811892463388
PAYUNI_PRODUCTION_HASH_KEY=bWoacqmMTH1o14jJHSWsgBIy3u5KKqbI
PAYUNI_PRODUCTION_HASH_IV=DeN566gVaWyrjvbh
```

#### 3. Google Sheets 憑證
```bash
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

#### 4. Google Sheets ID
```bash
# 測試環境 Sheets
GOOGLE_SANDBOX_SHEET_ID=1STjvYv5a8BFJS8v0sdcsHjoqoe4QgEPnjgNNk4qZST8
GOOGLE_SANDBOX_WATCH_SHEET_ID=1qloy7XgaGuht4zXr7-bl0FFCXyq-lB6fVMtNWJv64NY
GOOGLE_SANDBOX_BLOG_SHEET_ID=1yh7vaI5HP2kgJGQu_OnbIz__ZfNexf5ZleHqTiyOhVg

# 正式環境 Sheets（建議使用獨立的正式環境 Sheets）
GOOGLE_PRODUCTION_SHEET_ID=your_production_sheet_id
GOOGLE_PRODUCTION_WATCH_SHEET_ID=your_production_watch_sheet_id
GOOGLE_PRODUCTION_BLOG_SHEET_ID=your_production_blog_sheet_id
```

#### 5. URL 設定
```bash
NEXT_PUBLIC_BASE_URL=https://your-domain.com
PAYUNI_NOTIFY_URL=https://your-domain.com/api/webhook/payment
```

#### 6. GTM 設定
```bash
# 測試環境 GTM
NEXT_PUBLIC_GTM_SANDBOX_ID=GTM-TEST123
NEXT_PUBLIC_GTM_SANDBOX_PROXY_DOMAIN=gtm.pangea.weaven.co

# 正式環境 GTM（建議設定獨立的正式環境 GTM）
NEXT_PUBLIC_GTM_PRODUCTION_ID=GTM-PROD123
NEXT_PUBLIC_GTM_PRODUCTION_PROXY_DOMAIN=gtm.pangea.weaven.co
```

### **🟡 可選設定的環境變數**

#### 1. Meta Pixel（如果啟用 CAPI）
```bash
# 只有在 NEXT_PUBLIC_CAPI_ENABLED=true 時才需要設定
NEXT_PUBLIC_CAPI_ENABLED=false  # 預設關閉

# 如果啟用 CAPI，需要設定以下變數：
META_SANDBOX_PIXEL_ID=************5156
META_SANDBOX_ACCESS_TOKEN=your_sandbox_token
META_PRODUCTION_PIXEL_ID=your_production_pixel_id
META_PRODUCTION_ACCESS_TOKEN=your_production_token
```

#### 2. reCAPTCHA（如果啟用）
```bash
# 只有在 NEXT_PUBLIC_RECAPTCHA_ENABLED=true 時才需要設定
NEXT_PUBLIC_RECAPTCHA_ENABLED=false  # 預設關閉

# 如果啟用 reCAPTCHA，需要設定以下變數：
NEXT_PUBLIC_RECAPTCHA_SANDBOX_SITE_KEY=your_sandbox_site_key
RECAPTCHA_SANDBOX_SECRET_KEY=your_sandbox_secret_key
NEXT_PUBLIC_RECAPTCHA_PRODUCTION_SITE_KEY=your_production_site_key
RECAPTCHA_PRODUCTION_SECRET_KEY=your_production_secret_key
```

### **🟢 有預設值的環境變數（通常不需要設定）**

#### 1. 快取系統設定
```bash
# 這些變數有合理的預設值，通常不需要設定
# CACHE_ENABLED=true          # 預設啟用
# CACHE_TTL=300000            # 預設 5 分鐘（僅影響通用快取）
# CACHE_MAX_SIZE=50           # 預設 50 個項目
# CACHE_LOGGING=false         # 生產環境建議關閉（開發環境自動啟用）
```

## ✅ 部署檢查清單

### **部署前檢查**
- [ ] 確認 `APP_ENVIRONMENT=production`
- [ ] 確認所有 PayUni 憑證已設定
- [ ] 確認 Google Sheets 憑證已設定
- [ ] 確認正式環境的 Google Sheets ID 已設定
- [ ] 確認 URL 設定正確（NEXT_PUBLIC_BASE_URL, PAYUNI_NOTIFY_URL）
- [ ] 確認 GTM 正式環境 ID 已設定
- [ ] 運行 `npm run verify:secrets` 檢查環境變數
- [ ] 運行 `npm run build` 確認建置成功
- [ ] 運行 `npm run test` 確認測試通過

### **部署後檢查**
- [ ] 訪問 `/api/environment-check` 確認環境配置正確
- [ ] 測試部落格列表載入
- [ ] 測試手錶列表載入
- [ ] 測試 FAQ 頁面載入
- [ ] 測試支付流程（使用測試卡號）
- [ ] 檢查 Google Sheets 資料寫入
- [ ] 確認 GTM 追蹤正常運作

### 🔄 已完成的改進項目

#### 1. ✅ 統一環境配置系統
- 使用 `APP_ENVIRONMENT` 統一控制所有服務環境
- 自動根據環境選擇對應的配置
- 保持向後相容性

#### 2. ✅ Google Sheets 環境區分
- 為測試環境和正式環境創建獨立的 Google Sheets
- 使用環境特定的 Sheet ID
- 自動根據 APP_ENVIRONMENT 選擇對應的 Sheet

#### 3. ✅ 快取系統最佳化
- 實施多層次快取策略
- 根據資料特性設定不同的快取時間
- 自動預熱機制提升首次載入速度

#### 4. ✅ 環境變數驗證機制
- 自動檢查必要環境變數是否設定
- 提供詳細的錯誤訊息和設定建議
- 支援本地開發和 CI/CD 環境檢查

## 🚀 部署平台設定指南

### **Cloudflare Pages 設定**

#### 必須設定的環境變數：
```bash
# 環境控制
APP_ENVIRONMENT=production

# PayUni 憑證（所有環境）
PAYUNI_SANDBOX_MER_ID=your_sandbox_merchant_id
PAYUNI_SANDBOX_HASH_KEY=your_sandbox_hash_key
PAYUNI_SANDBOX_HASH_IV=your_sandbox_hash_iv
PAYUNI_PRODUCTION_MER_ID=your_production_merchant_id
PAYUNI_PRODUCTION_HASH_KEY=your_production_hash_key
PAYUNI_PRODUCTION_HASH_IV=your_production_hash_iv

# Google Sheets 憑證
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Google Sheets ID（所有環境）
GOOGLE_SANDBOX_SHEET_ID=your_sandbox_sheet_id
GOOGLE_SANDBOX_WATCH_SHEET_ID=your_sandbox_watch_sheet_id
GOOGLE_SANDBOX_BLOG_SHEET_ID=your_sandbox_blog_sheet_id
GOOGLE_PRODUCTION_SHEET_ID=your_production_sheet_id
GOOGLE_PRODUCTION_WATCH_SHEET_ID=your_production_watch_sheet_id
GOOGLE_PRODUCTION_BLOG_SHEET_ID=your_production_blog_sheet_id

# URL 設定
NEXT_PUBLIC_BASE_URL=https://your-domain.com
PAYUNI_NOTIFY_URL=https://your-domain.com/api/webhook/payment

# GTM 設定
NEXT_PUBLIC_GTM_SANDBOX_ID=GTM-TEST123
NEXT_PUBLIC_GTM_PRODUCTION_ID=GTM-PROD123
```

#### 建議設定的環境變數：
```bash
# 快取設定（生產環境最佳化）
CACHE_LOGGING=false         # 關閉日誌以提升效能
```

### **Vercel 設定**

Vercel 的環境變數設定與 Cloudflare Pages 相同，只需在 Vercel Dashboard 的 **Settings** > **Environment Variables** 中添加上述變數。

## 🎯 常見問題

### Q: 是否需要設定所有快取相關的環境變數？
**A:** 不需要。快取系統有合理的預設值，只有在需要特殊調整時才需要設定。

### Q: APP_ENVIRONMENT 和 NODE_ENV 的區別？
**A:**
- `APP_ENVIRONMENT`: 控制業務邏輯環境（sandbox/production）
- `NODE_ENV`: 控制 Next.js 框架行為（由部署平台自動設定）

### Q: 修改快取 TTL 是否影響部署配置？
**A:** 不影響。快取 TTL 已在程式碼中固定設定，不需要額外的環境變數配置。

### Q: 如何確認環境變數設定正確？
**A:** 運行 `npm run verify:secrets` 或訪問部署後的 `/api/environment-check` 端點。

### Q: 為什麼需要設定測試和正式環境的所有憑證？
**A:** 因為應用可能需要在不同環境間切換，設定所有憑證確保環境切換時不會出錯。

### Q: Google Sheets 私鑰格式問題？
**A:** 確保私鑰包含完整的 `-----BEGIN PRIVATE KEY-----` 和 `-----END PRIVATE KEY-----`，並且換行符使用 `\n`。

## 📚 相關工具和命令

### 環境變數檢查
```bash
# 檢查本地環境變數設定
npm run verify:secrets

# 檢查部署後的環境配置
curl https://your-domain.com/api/environment-check
```

### 測試命令
```bash
# 本地開發測試
npm run dev

# 建置測試
npm run build

# 單元測試
npm run test

# CPU 限制測試（Cloudflare Pages）
npm run cpu-check
```

---

> **💡 提示**
> 如需詳細的部署步驟和平台比較，請參考 [部署指南](./deployment-guide.md)。
> 如需測試相關的環境配置，請參考 [測試指南](./testing-guide.md)。
