# 防機器人攻擊系統

本文件說明網站表單系統的防機器人攻擊功能實作與使用方式。

## 功能概述

我們實作了多層防護機制來保護表單免受機器人攻擊：

### 第一層：基礎防護（已實作）
1. **蜜罐欄位（Honeypot）** - 隱藏欄位捕捉機器人
2. **提交時間檢查** - 防止過快或過慢的提交
3. **重複提交檢查** - 限制短時間內的重複提交
4. **User Agent 分析** - 檢測可疑的請求來源

### 第二層：速率限制（已實作）
1. **IP 基礎限制** - 每分鐘最多 5 次請求
2. **Email 基礎限制** - 每 5 分鐘最多 2 次提交

### 第三層：智能驗證（可選）
1. **reCAPTCHA v3** - Google 無感驗證（已準備，需配置）

## 技術實作

### 核心檔案結構
```
src/
├── lib/
│   ├── anti-bot.ts           # 防機器人核心邏輯
│   ├── rate-limiter.ts       # 速率限制功能
│   └── recaptcha.ts          # reCAPTCHA 驗證
├── components/ui/
│   └── honeypot-field.tsx    # 蜜罐欄位組件
├── hooks/
│   └── useFormSecurity.ts    # 表單安全 Hook
└── config/
    └── environment-config.ts # 環境配置（已更新）
```

### 前端整合

#### 1. 使用表單安全 Hook
```typescript
import { useFormSecurity } from '@/hooks/useFormSecurity';

function MyForm() {
  const {
    honeypotValue,
    formStartTime,
    securityToken,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  } = useFormSecurity();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // 安全驗證
    const validation = validateBeforeSubmit();
    if (!validation.isValid) {
      alert(validation.reason);
      return;
    }
    
    // 提交表單...
  };
}
```

#### 2. 添加隱藏安全欄位
```typescript
import { HoneypotField, TimestampField, SecurityTokenField } from '@/components/ui/honeypot-field';

<form onSubmit={handleSubmit}>
  {/* 安全防護欄位 - 對使用者隱藏 */}
  <HoneypotField 
    value={honeypotValue} 
    onChange={setHoneypotValue} 
  />
  <TimestampField startTime={formStartTime} />
  <SecurityTokenField token={securityToken} />
  
  {/* 正常表單欄位 */}
  {/* ... */}
</form>
```

### 後端驗證

#### API 路由整合範例
```typescript
import { validateSubmission } from '@/lib/anti-bot';
import { checkRateLimit, recordRequest, getClientIP } from '@/lib/rate-limiter';

export async function POST(request: NextRequest) {
  const data = await request.json();
  const clientIP = getClientIP(request);

  // 1. 速率限制檢查
  const rateLimit = checkRateLimit(`ip:${clientIP}`);
  if (!rateLimit.allowed) {
    return NextResponse.json(
      { error: '請求過於頻繁，請稍後再試' },
      { status: 429 }
    );
  }

  // 2. 安全驗證
  if (data.security) {
    const validation = validateSubmission({
      email: data.email,
      formStartTime: data.security.formStartTime,
      submissionTime: data.security.submissionTime,
      honeypotValue: data.security.honeypotValue,
      userAgent: data.security.userAgent,
      ip: clientIP,
    });

    if (!validation.isValid) {
      recordRequest(`ip:${clientIP}`, false);
      return NextResponse.json(
        { error: '表單驗證失敗' },
        { status: 400 }
      );
    }
  }

  // 3. 處理正常請求
  recordRequest(`ip:${clientIP}`, true);
  // ... 處理表單資料
}
```

## 配置說明

### 環境變數

#### reCAPTCHA（可選）
```env
# 啟用 reCAPTCHA
NEXT_PUBLIC_RECAPTCHA_ENABLED=true

# 開發環境
NEXT_PUBLIC_RECAPTCHA_SANDBOX_SITE_KEY=your_sandbox_site_key
RECAPTCHA_SANDBOX_SECRET_KEY=your_sandbox_secret_key

# 生產環境
NEXT_PUBLIC_RECAPTCHA_PRODUCTION_SITE_KEY=your_production_site_key
RECAPTCHA_PRODUCTION_SECRET_KEY=your_production_secret_key
```

### 防護參數調整

#### 修改預設配置
```typescript
// src/lib/anti-bot.ts
export const CUSTOM_ANTI_BOT_CONFIG = {
  minSubmissionTime: 5,     // 最小提交時間（秒）
  maxSubmissionTime: 3600,  // 最大提交時間（秒）
  duplicateSubmissionWindow: 10, // 重複提交檢查窗口（分鐘）
  honeypotFieldName: 'website',
};

// src/lib/rate-limiter.ts
export const CUSTOM_RATE_LIMIT_CONFIG = {
  windowMs: 60 * 1000,      // 時間窗口（毫秒）
  maxRequests: 3,           // 最大請求數
};
```

## 效果評估

### 預期防護效果
- **蜜罐欄位**：可阻擋 80%+ 的簡單機器人
- **提交時間檢查**：可阻擋自動化腳本
- **速率限制**：可防止大規模攻擊
- **reCAPTCHA v3**：可處理更複雜的攻擊

### 監控指標
- 被拒絕的請求數量和原因
- 速率限制觸發頻率
- reCAPTCHA 分數分佈
- 正常用戶的影響程度

## 性能考量

### CPU 使用量（適用於 Cloudflare Pages 10ms 限制）
- 蜜罐欄位驗證：~0ms
- 提交時間檢查：<1ms
- 速率限制檢查：1-2ms
- reCAPTCHA 驗證：2-3ms
- **總計**：<6ms（符合限制）

### 記憶體使用
- 速率限制快取：每個 IP/Email 約 100 bytes
- 自動清理過期記錄，避免記憶體洩漏

## 測試

### 執行測試
```bash
# 執行防機器人功能測試
npm test anti-bot

# 執行完整測試套件
npm test
```

### 手動測試場景
1. **正常提交**：應該成功
2. **快速提交**：應該被拒絕
3. **填寫蜜罐欄位**：應該被拒絕
4. **重複提交**：短時間內應該被拒絕
5. **大量請求**：應該觸發速率限制

## 故障排除

### 常見問題

#### 1. 正常用戶被誤判
- 檢查提交時間閾值是否過嚴格
- 確認蜜罐欄位是否正確隱藏
- 調整 reCAPTCHA 分數閾值

#### 2. 機器人仍能通過
- 啟用 reCAPTCHA v3
- 降低速率限制閾值
- 添加更多蜜罐欄位

#### 3. 性能問題
- 檢查速率限制快取大小
- 確認清理機制正常運作
- 監控 CPU 使用量

### 日誌分析
```bash
# 查看安全驗證日誌
grep "🔒 安全驗證" logs/
grep "🚫" logs/  # 被拒絕的請求
grep "✅" logs/  # 通過驗證的請求
```

## 未來擴展

### 可能的改進方向
1. **機器學習模型**：基於行為模式的檢測
2. **設備指紋**：更精確的用戶識別
3. **地理位置檢查**：異常地區的請求過濾
4. **時間模式分析**：檢測異常的提交時間模式

### 與第三方服務整合
- Cloudflare Bot Management
- AWS WAF
- 其他專業防護服務
