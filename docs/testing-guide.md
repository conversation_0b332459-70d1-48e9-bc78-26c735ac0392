# Pangea Website 測試指南

## 概述

本專案採用多層次的測試策略，確保核心業務流程的穩定性和可靠性。

## 測試架構

### 測試類型

1. **單元測試** (Unit Tests) - Jest + Testing Library
   - 測試個別函數和組件
   - 快速執行，高覆蓋率
   - 位置：`src/__tests__/unit/`

2. **整合測試** (Integration Tests) - Jest + MSW
   - 測試 API 端點和服務整合
   - 模擬外部 API 調用
   - 位置：`src/__tests__/integration/`

3. **E2E 測試** (End-to-End Tests) - Playwright
   - 測試完整用戶流程
   - 真實瀏覽器環境
   - 位置：`src/__tests__/e2e/`

### 測試覆蓋範圍

#### 高優先級
- ✅ 表單提交流程
- ✅ PayUni 付款整合
- ✅ 訂單查詢功能
- ✅ 表單驗證邏輯
- ✅ 場次名額控制

#### 中優先級
- ✅ 頁面渲染測試
- ✅ 響應式設計
- ✅ 錯誤處理
- ✅ 狀態管理

## 快速開始

### 安裝依賴

```bash
npm install
```

### 執行所有測試

```bash
npm run test:all
```

### 執行特定類型測試

```bash
# 單元測試
npm run test:unit

# 整合測試
npm run test:integration

# E2E 測試
npm run test:e2e

# 測試覆蓋率
npm run test:coverage
```

### 使用測試腳本

```bash
# 執行所有測試
./scripts/run-tests.sh

# 執行特定類型
./scripts/run-tests.sh unit
./scripts/run-tests.sh integration
./scripts/run-tests.sh e2e
```

## 環境配置

### 測試環境變數

創建 `.env.test` 文件：

```env
# PayUni 沙盒環境
PAYUNI_ENVIRONMENT=sandbox
PAYUNI_SANDBOX_MERCHANT_ID=S01421169
PAYUNI_SANDBOX_HASH_KEY=your_test_hash_key
PAYUNI_SANDBOX_HASH_IV=your_test_hash_iv

# Google Sheets 測試
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY=your_test_private_key
GOOGLE_SHEET_ID=test_sheet_id

# Meta CAPI 測試
META_PIXEL_ID=test_pixel_id
META_ACCESS_TOKEN=test_access_token
```

### CI/CD 環境

在 GitHub Secrets 中設置：
- `PAYUNI_SANDBOX_MERCHANT_ID`
- `PAYUNI_SANDBOX_HASH_KEY`
- `PAYUNI_SANDBOX_HASH_IV`
- `GOOGLE_SERVICE_ACCOUNT_EMAIL`
- `GOOGLE_PRIVATE_KEY`
- `TEST_GOOGLE_SHEET_ID`
- `META_PIXEL_ID`
- `META_ACCESS_TOKEN`

## 測試最佳實踐

### 1. 測試命名

```typescript
describe('功能模組名稱', () => {
  test('應該在特定條件下執行預期行為', () => {
    // 測試內容
  });
});
```

### 2. 測試結構 (AAA Pattern)

```typescript
test('應該驗證有效的 email 格式', () => {
  // Arrange - 準備測試資料
  const validEmail = '<EMAIL>';
  
  // Act - 執行被測試的功能
  const result = validateEmail(validEmail);
  
  // Assert - 驗證結果
  expect(result).toBe(true);
});
```

### 3. Mock 使用

```typescript
// Mock 外部依賴
jest.mock('@/lib/google-sheets', () => ({
  appendToSheet: jest.fn().mockResolvedValue({ success: true })
}));
```

### 4. 測試資料管理

使用 fixtures 管理測試資料：

```typescript
import { validFormData } from '../fixtures/form-data';
```

## 常見問題

### Q: 測試執行緩慢
A: 檢查是否有不必要的外部 API 調用，使用 Mock 替代。

### Q: E2E 測試不穩定
A: 增加適當的等待時間，使用 `waitForSelector` 等待元素載入。

### Q: 測試覆蓋率不足
A: 檢查 `jest.config.js` 中的 `collectCoverageFrom` 設定。

## 持續改進

### 測試覆蓋率目標
- 函數覆蓋率：≥ 70%
- 分支覆蓋率：≥ 70%
- 行覆蓋率：≥ 70%
- 語句覆蓋率：≥ 70%

### 監控指標
- 測試執行時間
- 測試成功率
- 覆蓋率趨勢
- 缺陷發現率

## 相關資源

- [Jest 官方文檔](https://jestjs.io/)
- [Testing Library 指南](https://testing-library.com/)
- [Playwright 文檔](https://playwright.dev/)
- [MSW 使用指南](https://mswjs.io/)
