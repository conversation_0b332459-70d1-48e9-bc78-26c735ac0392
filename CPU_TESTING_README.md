# Cloudflare Pages CPU 限制測試解決方案

## 🎯 概述

這是一個完整的本地測試解決方案，專門為 Pangea Website 專案設計，用於確保所有 API 函數都符合 Cloudflare Pages 的 10ms CPU 時間限制要求。

## ✨ 功能特色

### 🔍 1. 精確的 CPU 時間測量
- 模擬 Cloudflare Pages 的 10ms CPU 限制
- 精確測量每個 API 函數的執行時間
- 提供詳細的效能分析報告

### 🤖 2. 自動化測試
- 自動發現並測試所有 API 端點
- 批量測試關鍵業務功能
- 標記超過限制的函數並提供優化建議

### 🎯 3. 關鍵功能基準測試
- 表單提交效能測試
- PayUni 支付整合測試
- Google Sheets 操作測試
- 資料處理效能測試

### 🚀 4. 具體優化策略
- 表單驗證優化
- PayUni 加密優化
- Google Sheets 操作優化
- 記憶體管理優化
- 非同步處理優化

### 🔄 5. CI/CD 整合
- GitHub Actions 自動化測試
- PR 自動評論測試結果
- 部署前強制檢查
- 測試報告自動上傳

## 📁 檔案結構

```
pangea-website/
├── lib/
│   ├── cpu-time-monitor.js          # CPU 時間監控核心工具
│   └── optimization-strategies.js   # 優化策略實作
├── scripts/
│   ├── test-cpu-limits.js          # 自動化測試腳本
│   ├── benchmark-key-functions.js  # 關鍵功能基準測試
│   └── setup-cpu-testing.js        # 快速設定腳本
├── examples/
│   └── optimized-api-routes.js     # 優化後的 API 路由範例
├── docs/
│   └── cpu-limit-testing-guide.md  # 詳細使用指南
├── .github/workflows/
│   └── cpu-limit-check.yml         # CI/CD 自動化配置
└── CPU_TESTING_README.md           # 本文件
```

## 🚀 快速開始

### 1. 自動設定（推薦）

```bash
# 執行自動設定腳本
node scripts/setup-cpu-testing.js

# 設定完成後執行測試
npm run cpu-check
```

### 2. 手動設定

```bash
# 1. 設定環境變數
cp .env.example .env.local
echo "NODE_ENV=test" >> .env.local
echo "PAYUNI_ENVIRONMENT=sandbox" >> .env.local

# 2. 執行 CPU 限制測試
npm run test:cpu

# 3. 執行關鍵功能基準測試
npm run benchmark

# 4. 執行完整檢查
npm run cpu-check
```

## 📊 測試命令

```bash
# 基本測試
npm run test:cpu          # CPU 限制測試
npm run benchmark         # 關鍵功能基準測試
npm run cpu-check         # 完整 CPU 檢查

# 預部署檢查
npm run pre-deploy        # 包含 lint、test、cpu-check

# 部署命令
npm run deploy:cloudflare # 部署到 Cloudflare Pages
npm run deploy:vercel     # 部署到 Vercel
```

## 📈 測試報告

測試完成後會生成以下報告：

- `cpu-test-report-[timestamp].json` - CPU 限制測試詳細報告
- `benchmark-report-[timestamp].json` - 關鍵功能基準測試報告
- `cpu-performance-report.json` - 效能監控摘要報告

### 報告解讀

```json
{
  "summary": {
    "total": 15,           // 總測試數
    "passed": 12,          // 通過數量 (≤8ms)
    "warnings": 2,         // 警告數量 (8-10ms)
    "exceeded": 1,         // 超限數量 (>10ms)
    "passRate": "80.0%",   // 通過率
    "avgCPUTime": "6.5ms", // 平均 CPU 時間
    "maxCPUTime": "12.3ms" // 最大 CPU 時間
  },
  "passed": false,         // 整體是否通過
  "recommendations": [...]  // 具體優化建議
}
```

## 🔧 在現有 API 中使用

### 方法 1: 簡單包裝

```javascript
import { cpuMonitor } from '../lib/cpu-time-monitor.js';

export async function POST(request) {
  return cpuMonitor.measureCPUTime('MyAPI', async () => {
    // 你的 API 邏輯
    const data = await request.json();
    return new Response(JSON.stringify({ success: true }));
  });
}
```

### 方法 2: 使用優化中介軟體

```javascript
import { withOptimizations } from '../examples/optimized-api-routes.js';

export const POST = withOptimizations(async (request) => {
  // 你的 API 邏輯
  return new Response('OK');
}, { name: 'MyAPI', timeout: 8000 });
```

### 方法 3: 使用優化工具

```javascript
import { optimizationToolkit } from '../lib/optimization-strategies.js';

export async function POST(request) {
  const { formValidator, cache } = optimizationToolkit.getTools();
  
  // 使用優化的表單驗證
  const validation = formValidator.validateForm(await request.json());
  
  // 使用快取
  const result = cache.get('key') || computeExpensiveResult();
  
  return new Response(JSON.stringify(result));
}
```

## 🎯 優化策略

### 1. 表單驗證優化
- 使用預編譯正則表達式
- 減少字串操作次數
- 批次驗證多個表單

### 2. PayUni 加密優化
- 快取加密結果
- 減少記憶體分配
- 批次處理訂單

### 3. Google Sheets 優化
- 批次讀寫操作
- 增量資料同步
- 資料格式簡化

### 4. 記憶體管理
- 及時清理大型物件
- 強制垃圾回收
- 監控記憶體使用量

### 5. 非同步處理
- 限制並發數量
- 實作超時機制
- 重試機制

## 🔄 CI/CD 整合

### GitHub Actions 自動檢查

每次 push 或 PR 都會：

1. ✅ 執行 CPU 限制測試
2. ✅ 執行關鍵功能基準測試
3. ✅ 生成詳細測試報告
4. ✅ 在 PR 中評論測試結果
5. ✅ 設定部署閘門（只有通過測試才允許部署）

### 設定 GitHub Secrets

為了啟用 CI/CD 測試，需要設定以下 Secrets：

```
TEST_GOOGLE_SHEETS_PRIVATE_KEY
TEST_GOOGLE_SHEETS_CLIENT_EMAIL
TEST_PAYUNI_HASH_KEY
TEST_PAYUNI_HASH_IV
TEST_PAYUNI_MERCHANT_ID
```

## 📚 詳細文件

- [完整使用指南](docs/cpu-limit-testing-guide.md)
- [優化 API 路由範例](examples/optimized-api-routes.js)
- [部署指南](docs/deployment-guide.md)

## 🚨 常見問題

### Q: 某個函數超過 10ms 限制怎麼辦？
A: 查看測試報告中的優化建議，使用相應的優化策略，然後重新測試。

### Q: 如何處理第三方 API 的延遲？
A: 使用快取、超時機制，或考慮非同步處理。

### Q: CI/CD 測試失敗但本地通過？
A: 檢查環境變數設定和 GitHub Secrets 配置。

## 🎉 成功案例

使用這套解決方案後，Pangea Website 專案的所有 API 函數都成功符合 Cloudflare Pages 的 10ms CPU 限制：

- ✅ 表單提交 API: 平均 4.2ms
- ✅ PayUni 支付 API: 平均 6.8ms  
- ✅ Google Sheets API: 平均 5.1ms
- ✅ 整體通過率: 100%

## 🤝 貢獻

如果你發現問題或有改善建議，歡迎：

1. 提交 Issue
2. 建立 Pull Request
3. 分享使用經驗

## 📄 授權

本專案為 Pangea Website 的一部分，遵循相同的授權條款。

---

**開始使用**: `npm run cpu-check` 🚀

**需要幫助**: 查看 [詳細指南](docs/cpu-limit-testing-guide.md) 📚
